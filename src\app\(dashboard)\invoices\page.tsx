'use client'

import { useState, useEffect } from 'react'
import { Plus, Search, Filter, FileText, Eye, Edit, Trash2, Printer } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { formatCurrency, formatDate } from '@/lib/utils'
import { realDataManager, Sale } from '@/lib/real-storage'
import Link from 'next/link'
import PrintManager from '@/components/print/PrintManager'
import InvoicePrint from '@/components/print/InvoicePrint'

export default function InvoicesPage() {
  const [sales, setSales] = useState<Sale[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null)
  const [detailsModalOpen, setDetailsModalOpen] = useState(false)
  const [printModalOpen, setPrintModalOpen] = useState(false)

  useEffect(() => {
    realDataManager.initializeData()
    loadSales()
  }, [])

  const loadSales = () => {
    setSales(realDataManager.getSales())
  }

  const filteredSales = sales.filter(sale =>
    sale.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    sale.id.includes(searchTerm)
  )

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">مكتملة</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">معلقة</Badge>
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800">ملغية</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getPaymentTypeBadge = (paymentType: string) => {
    switch (paymentType) {
      case 'cash':
        return <Badge className="bg-blue-100 text-blue-800">نقدي</Badge>
      case 'credit':
        return <Badge className="bg-orange-100 text-orange-800">آجل</Badge>
      case 'partial':
        return <Badge className="bg-purple-100 text-purple-800">جزئي</Badge>
      default:
        return <Badge>{paymentType}</Badge>
    }
  }

  const printInvoice = (sale: Sale) => {
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <html dir="rtl">
          <head>
            <title>فاتورة رقم ${sale.id.slice(-6)}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .invoice-details { margin-bottom: 20px; }
              .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
              .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
              .items-table th { background-color: #f5f5f5; }
              .totals { margin-top: 20px; }
              .total-row { display: flex; justify-content: space-between; margin: 5px 0; }
              .final-total { font-weight: bold; font-size: 18px; border-top: 2px solid #000; padding-top: 10px; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>فاتورة مبيعات</h1>
              <h2>تجارة الحديد والمعدات</h2>
            </div>
            <div class="invoice-details">
              <p><strong>رقم الفاتورة:</strong> ${sale.id.slice(-6)}</p>
              <p><strong>التاريخ:</strong> ${new Date(sale.createdAt).toLocaleDateString('ar-SA')}</p>
              <p><strong>العميل:</strong> ${sale.customerName}</p>
              <p><strong>نوع الدفع:</strong> ${sale.paymentType === 'cash' ? 'نقدي' : sale.paymentType === 'credit' ? 'آجل' : 'جزئي'}</p>
            </div>
            <table class="items-table">
              <thead>
                <tr>
                  <th>المنتج</th>
                  <th>الكمية</th>
                  <th>السعر</th>
                  <th>الإجمالي</th>
                </tr>
              </thead>
              <tbody>
                ${sale.items.map(item => `
                  <tr>
                    <td>${item.productName}</td>
                    <td>${item.quantity}</td>
                    <td>${item.unitPrice.toLocaleString()} ر.ي</td>
                    <td>${item.total.toLocaleString()} ر.ي</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
            <div class="totals">
              <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span>${sale.subtotal.toLocaleString()} ر.ي</span>
              </div>
              ${sale.discount > 0 ? `
                <div class="total-row">
                  <span>الخصم:</span>
                  <span>-${sale.discount.toLocaleString()} ر.ي</span>
                </div>
              ` : ''}
              <div class="total-row">
                <span>الضريبة:</span>
                <span>${sale.tax.toLocaleString()} ر.ي</span>
              </div>
              <div class="total-row final-total">
                <span>الإجمالي النهائي:</span>
                <span>${sale.total.toLocaleString()} ر.ي</span>
              </div>
              <div class="total-row">
                <span>المبلغ المدفوع:</span>
                <span>${sale.paidAmount.toLocaleString()} ر.ي</span>
              </div>
              ${sale.remainingAmount > 0 ? `
                <div class="total-row">
                  <span>المبلغ المتبقي:</span>
                  <span>${sale.remainingAmount.toLocaleString()} ر.ي</span>
                </div>
              ` : ''}
            </div>
            ${sale.notes ? `<p><strong>ملاحظات:</strong> ${sale.notes}</p>` : ''}
          </body>
        </html>
      `)
      printWindow.document.close()
      printWindow.print()
    }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">الفواتير</h1>
        <Link href="/pos">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            فاتورة جديدة
          </Button>
        </Link>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الفواتير</p>
                <p className="text-2xl font-bold">{sales.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الفواتير المكتملة</p>
                <p className="text-2xl font-bold text-green-600">
                  {sales.filter(s => s.status === 'completed').length}
                </p>
              </div>
              <FileText className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الفواتير المعلقة</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {sales.filter(s => s.status === 'pending').length}
                </p>
              </div>
              <FileText className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المبيعات</p>
                <p className="text-2xl font-bold">
                  {sales.reduce((sum, sale) => sum + sale.total, 0).toLocaleString()} ر.ي
                </p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="البحث في الفواتير..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Invoices Table */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة الفواتير ({filteredSales.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredSales.length === 0 ? (
            <p className="text-center text-gray-500 py-8">لا توجد فواتير</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الفاتورة</TableHead>
                  <TableHead>العميل</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>المبلغ</TableHead>
                  <TableHead>نوع الدفع</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSales.map((sale) => (
                  <TableRow key={sale.id}>
                    <TableCell className="font-medium">
                      {sale.id.slice(-6)}
                    </TableCell>
                    <TableCell>{sale.customerName}</TableCell>
                    <TableCell>
                      {new Date(sale.createdAt).toLocaleDateString('ar-SA')}
                    </TableCell>
                    <TableCell>{sale.total.toLocaleString()} ر.ي</TableCell>
                    <TableCell>{getPaymentTypeBadge(sale.paymentType)}</TableCell>
                    <TableCell>{getStatusBadge(sale.status)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">فتح القائمة</span>
                            <FileText className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedSale(sale)
                              setDetailsModalOpen(true)
                            }}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            عرض التفاصيل
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => printInvoice(sale)}>
                            <Printer className="mr-2 h-4 w-4" />
                            طباعة
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Invoice Details Modal */}
      <Dialog open={detailsModalOpen} onOpenChange={setDetailsModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تفاصيل الفاتورة</DialogTitle>
          </DialogHeader>
          {selectedSale && (
            <div className="space-y-6">
              {/* Invoice Header */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold mb-2">معلومات الفاتورة</h3>
                  <p><strong>رقم الفاتورة:</strong> {selectedSale.id.slice(-6)}</p>
                  <p><strong>التاريخ:</strong> {new Date(selectedSale.createdAt).toLocaleDateString('ar-SA')}</p>
                  <p><strong>الحالة:</strong> {getStatusBadge(selectedSale.status)}</p>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">معلومات العميل</h3>
                  <p><strong>اسم العميل:</strong> {selectedSale.customerName}</p>
                  <p><strong>نوع الدفع:</strong> {getPaymentTypeBadge(selectedSale.paymentType)}</p>
                </div>
              </div>

              {/* Items Table */}
              <div>
                <h3 className="font-semibold mb-2">المنتجات</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>المنتج</TableHead>
                      <TableHead>الكمية</TableHead>
                      <TableHead>السعر</TableHead>
                      <TableHead>الإجمالي</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedSale.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.productName}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{item.unitPrice.toLocaleString()} ر.ي</TableCell>
                        <TableCell>{item.total.toLocaleString()} ر.ي</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Totals */}
              <div className="border-t pt-4">
                <div className="flex justify-between mb-2">
                  <span>المجموع الفرعي:</span>
                  <span>{selectedSale.subtotal.toLocaleString()} ر.ي</span>
                </div>
                {selectedSale.discount > 0 && (
                  <div className="flex justify-between mb-2 text-red-600">
                    <span>الخصم:</span>
                    <span>-{selectedSale.discount.toLocaleString()} ر.ي</span>
                  </div>
                )}
                <div className="flex justify-between mb-2">
                  <span>الضريبة:</span>
                  <span>{selectedSale.tax.toLocaleString()} ر.ي</span>
                </div>
                <div className="flex justify-between mb-2 font-bold text-lg border-t pt-2">
                  <span>الإجمالي النهائي:</span>
                  <span>{selectedSale.total.toLocaleString()} ر.ي</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>المبلغ المدفوع:</span>
                  <span>{selectedSale.paidAmount.toLocaleString()} ر.ي</span>
                </div>
                {selectedSale.remainingAmount > 0 && (
                  <div className="flex justify-between mb-2 text-red-600 font-semibold">
                    <span>المبلغ المتبقي:</span>
                    <span>{selectedSale.remainingAmount.toLocaleString()} ر.ي</span>
                  </div>
                )}
              </div>

              {selectedSale.notes && (
                <div>
                  <h3 className="font-semibold mb-2">ملاحظات</h3>
                  <p className="bg-gray-50 p-3 rounded">{selectedSale.notes}</p>
                </div>
              )}

              <div className="flex gap-2">
                <Button onClick={() => printInvoice(selectedSale)}>
                  <Printer className="mr-2 h-4 w-4" />
                  طباعة الفاتورة
                </Button>
                <Button variant="outline" onClick={() => setDetailsModalOpen(false)}>
                  إغلاق
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* مودال الطباعة المتقدم */}
      <Dialog open={printModalOpen} onOpenChange={setPrintModalOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>طباعة الفاتورة</DialogTitle>
          </DialogHeader>
          {selectedSale && (
            <PrintManager
              filename={`invoice-${selectedSale.id.slice(-6)}`}
              title={`فاتورة رقم ${selectedSale.id.slice(-6)}`}
            >
              <InvoicePrint
                invoice={{
                  id: selectedSale.id,
                  invoiceNumber: `INV-${selectedSale.id.slice(-6)}`,
                  customerName: selectedSale.customerName,
                  customerEmail: '',
                  customerPhone: '',
                  customerAddress: '',
                  issueDate: new Date(selectedSale.createdAt).toISOString(),
                  dueDate: new Date(selectedSale.createdAt + 30 * 24 * 60 * 60 * 1000).toISOString(),
                  items: selectedSale.items.map(item => ({
                    id: item.id,
                    description: item.productName,
                    quantity: item.quantity,
                    unitPrice: item.unitPrice,
                    total: item.total
                  })),
                  subtotal: selectedSale.subtotal,
                  taxAmount: selectedSale.tax,
                  discountAmount: selectedSale.discount,
                  total: selectedSale.total,
                  notes: selectedSale.notes || '',
                  status: selectedSale.status
                }}
              />
            </PrintManager>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

