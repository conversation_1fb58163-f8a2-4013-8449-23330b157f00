'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Calculator, FileText, Users, TrendingUp, BarChart3, Receipt, LogIn, UserPlus } from 'lucide-react'
import Link from 'next/link'
import { useAuth, FallbackSignInButton, FallbackSignUpButton, FallbackUserButton } from '@/lib/simple-auth'

export default function HomePage() {
  const { isSignedIn, user } = useAuth()
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Calculator className="h-8 w-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">نظام المحاسبة</h1>
          </div>
          <div className="flex items-center gap-4">
            {isSignedIn ? (
              <>
                <span className="text-sm text-gray-600">
                  مرحباً، {user?.firstName || 'المستخدم'}
                </span>
                <Link href="/dashboard">
                  <Button>لوحة التحكم</Button>
                </Link>
                <FallbackUserButton afterSignOutUrl="/" />
              </>
            ) : (
              <>
                <FallbackSignInButton mode="modal">
                  <Button variant="outline" className="flex items-center gap-2">
                    <LogIn className="h-4 w-4" />
                    تسجيل الدخول
                  </Button>
                </FallbackSignInButton>
                <FallbackSignUpButton mode="modal">
                  <Button className="flex items-center gap-2">
                    <UserPlus className="h-4 w-4" />
                    إنشاء حساب
                  </Button>
                </FallbackSignUpButton>
              </>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-gray-900 mb-4">
            نظام المحاسبة الحديث
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            إدارة شاملة لأعمالك المحاسبية مع أحدث التقنيات وواجهة سهلة الاستخدام
          </p>
          <div className="flex gap-4 justify-center">
            <Link href="/pos">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                دخول النظام
              </Button>
            </Link>
            <Button variant="outline" size="lg">
              تعرف على المزيد
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <FileText className="h-12 w-12 text-blue-600 mb-2" />
              <CardTitle>إدارة الفواتير</CardTitle>
              <CardDescription>
                إنشاء وإدارة الفواتير بسهولة مع حسابات تلقائية للضرائب
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <Users className="h-12 w-12 text-green-600 mb-2" />
              <CardTitle>إدارة العملاء</CardTitle>
              <CardDescription>
                قاعدة بيانات شاملة لعملائك مع تتبع المدفوعات
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <BarChart3 className="h-12 w-12 text-purple-600 mb-2" />
              <CardTitle>التقارير المالية</CardTitle>
              <CardDescription>
                تقارير مفصلة وتحليلات مالية لمساعدتك في اتخاذ القرارات
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <Receipt className="h-12 w-12 text-orange-600 mb-2" />
              <CardTitle>تتبع المصروفات</CardTitle>
              <CardDescription>
                تسجيل ومتابعة جميع المصروفات والإيرادات
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <TrendingUp className="h-12 w-12 text-red-600 mb-2" />
              <CardTitle>تحليل الأرباح</CardTitle>
              <CardDescription>
                تحليل مفصل للأرباح والخسائر مع التوقعات
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <Calculator className="h-12 w-12 text-indigo-600 mb-2" />
              <CardTitle>حسابات تلقائية</CardTitle>
              <CardDescription>
                حسابات الضرائب والخصومات تلقائياً
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-white rounded-lg p-8 shadow-lg">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            ابدأ رحلتك المحاسبية اليوم
          </h2>
          <p className="text-gray-600 mb-6 max-w-xl mx-auto">
            انضم إلى آلاف الشركات التي تستخدم نظامنا لإدارة أعمالها المحاسبية بكفاءة
          </p>
          <Link href="/pos">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              ابدأ الآن مجاناً
            </Button>
          </Link>
        </div>
      </main>
    </div>
  )
}
