import { initTRPC, TRPCError } from '@trpc/server'
import { type NextRequest } from 'next/server'
import { db } from '@/lib/db'
import { ZodError } from 'zod'

export const createTRPCContext = async (opts: { req: NextRequest; res: any }) => {
  const { req, res } = opts
  // للتطوير: استخدام user_id ثابت
  const userId = 'user_1'

  return {
    db,
    userId,
    req,
    res,
  }
}

const t = initTRPC.context<typeof createTRPCContext>().create({
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    }
  },
})

export const createTRPCRouter = t.router
export const publicProcedure = t.procedure

const enforceUserIsAuthed = t.middleware(({ ctx, next }) => {
  if (!ctx.userId) {
    throw new TRPCError({ code: 'UNAUTHORIZED' })
  }
  return next({
    ctx: {
      userId: ctx.userId,
    },
  })
})

export const protectedProcedure = t.procedure.use(enforceUserIsAuthed)
