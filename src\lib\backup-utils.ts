// نظام النسخ الاحتياطي واستعادة البيانات

import { realDataManager } from './real-storage'

export interface BackupData {
  version: string
  timestamp: number
  data: {
    products: any[]
    customers: any[]
    suppliers: any[]
    sales: any[]
    returns: any[]
    stockMovements: any[]
    payments: any[]
    expenses: any[]
  }
  metadata: {
    totalRecords: number
    createdBy: string
    description?: string
  }
}

// إنشاء نسخة احتياطية
export function createBackup(description?: string): BackupData {
  try {
    const products = realDataManager.getProducts()
    const customers = realDataManager.getCustomers()
    const suppliers = realDataManager.getSuppliers()
    const sales = realDataManager.getSales()
    const returns = realDataManager.getReturns()
    const stockMovements = realDataManager.getStockMovements()
    const payments = realDataManager.getPayments()
    const expenses = realDataManager.getExpenses()

    const backup: BackupData = {
      version: '1.0.0',
      timestamp: Date.now(),
      data: {
        products,
        customers,
        suppliers,
        sales,
        returns,
        stockMovements,
        payments,
        expenses
      },
      metadata: {
        totalRecords: products.length + customers.length + suppliers.length + 
                     sales.length + returns.length + stockMovements.length + 
                     payments.length + expenses.length,
        createdBy: 'system',
        description: description || `نسخة احتياطية تلقائية - ${new Date().toLocaleDateString('ar-SA')}`
      }
    }

    return backup
  } catch (error) {
    console.error('خطأ في إنشاء النسخة الاحتياطية:', error)
    throw new Error('فشل في إنشاء النسخة الاحتياطية')
  }
}

// تصدير النسخة الاحتياطية كملف
export function exportBackup(backup: BackupData, filename?: string) {
  try {
    const backupJson = JSON.stringify(backup, null, 2)
    const blob = new Blob([backupJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = filename || `backup-${new Date().toISOString().split('T')[0]}.json`
    link.style.display = 'none'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
    
    return true
  } catch (error) {
    console.error('خطأ في تصدير النسخة الاحتياطية:', error)
    throw new Error('فشل في تصدير النسخة الاحتياطية')
  }
}

// استيراد النسخة الاحتياطية من ملف
export function importBackupFromFile(file: File): Promise<BackupData> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (event) => {
      try {
        const content = event.target?.result as string
        const backup = JSON.parse(content) as BackupData
        
        // التحقق من صحة البيانات
        if (!validateBackup(backup)) {
          reject(new Error('ملف النسخة الاحتياطية غير صحيح'))
          return
        }
        
        resolve(backup)
      } catch (error) {
        reject(new Error('فشل في قراءة ملف النسخة الاحتياطية'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('خطأ في قراءة الملف'))
    }
    
    reader.readAsText(file)
  })
}

// التحقق من صحة النسخة الاحتياطية
export function validateBackup(backup: any): backup is BackupData {
  try {
    return (
      backup &&
      typeof backup === 'object' &&
      backup.version &&
      backup.timestamp &&
      backup.data &&
      backup.metadata &&
      Array.isArray(backup.data.products) &&
      Array.isArray(backup.data.customers) &&
      Array.isArray(backup.data.suppliers) &&
      Array.isArray(backup.data.sales) &&
      Array.isArray(backup.data.returns) &&
      Array.isArray(backup.data.stockMovements) &&
      Array.isArray(backup.data.payments) &&
      Array.isArray(backup.data.expenses)
    )
  } catch (error) {
    return false
  }
}

// استعادة البيانات من النسخة الاحتياطية
export function restoreFromBackup(backup: BackupData, options: {
  clearExisting?: boolean
  mergeData?: boolean
} = { clearExisting: true, mergeData: false }) {
  try {
    if (!validateBackup(backup)) {
      throw new Error('النسخة الاحتياطية غير صحيحة')
    }

    // مسح البيانات الموجودة إذا كان مطلوباً
    if (options.clearExisting) {
      localStorage.clear()
    }

    // استعادة البيانات
    const { data } = backup

    // حفظ البيانات في localStorage
    localStorage.setItem('products', JSON.stringify(data.products))
    localStorage.setItem('customers', JSON.stringify(data.customers))
    localStorage.setItem('suppliers', JSON.stringify(data.suppliers))
    localStorage.setItem('sales', JSON.stringify(data.sales))
    localStorage.setItem('returns', JSON.stringify(data.returns))
    localStorage.setItem('stockMovements', JSON.stringify(data.stockMovements))
    localStorage.setItem('payments', JSON.stringify(data.payments))
    localStorage.setItem('expenses', JSON.stringify(data.expenses))

    // حفظ معلومات الاستعادة
    localStorage.setItem('lastRestore', JSON.stringify({
      timestamp: Date.now(),
      backupTimestamp: backup.timestamp,
      version: backup.version,
      recordsRestored: backup.metadata.totalRecords
    }))

    return true
  } catch (error) {
    console.error('خطأ في استعادة البيانات:', error)
    throw new Error('فشل في استعادة البيانات من النسخة الاحتياطية')
  }
}

// إنشاء نسخة احتياطية تلقائية
export function createAutoBackup() {
  try {
    const backup = createBackup('نسخة احتياطية تلقائية')
    
    // حفظ النسخة الاحتياطية في localStorage
    const autoBackups = getAutoBackups()
    autoBackups.push({
      id: Date.now().toString(),
      timestamp: backup.timestamp,
      size: JSON.stringify(backup).length,
      description: backup.metadata.description || '',
      data: backup
    })

    // الاحتفاظ بآخر 10 نسخ احتياطية فقط
    const limitedBackups = autoBackups.slice(-10)
    localStorage.setItem('autoBackups', JSON.stringify(limitedBackups))

    return backup
  } catch (error) {
    console.error('خطأ في إنشاء النسخة الاحتياطية التلقائية:', error)
    throw error
  }
}

// جلب النسخ الاحتياطية التلقائية
export function getAutoBackups(): Array<{
  id: string
  timestamp: number
  size: number
  description: string
  data: BackupData
}> {
  try {
    const stored = localStorage.getItem('autoBackups')
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error('خطأ في جلب النسخ الاحتياطية:', error)
    return []
  }
}

// حذف نسخة احتياطية تلقائية
export function deleteAutoBackup(backupId: string) {
  try {
    const autoBackups = getAutoBackups()
    const filteredBackups = autoBackups.filter(backup => backup.id !== backupId)
    localStorage.setItem('autoBackups', JSON.stringify(filteredBackups))
    return true
  } catch (error) {
    console.error('خطأ في حذف النسخة الاحتياطية:', error)
    return false
  }
}

// جدولة النسخ الاحتياطي التلقائي
export function scheduleAutoBackup(intervalMinutes: number = 60) {
  // إنشاء نسخة احتياطية فورية
  createAutoBackup()

  // جدولة النسخ الاحتياطية التلقائية
  const interval = setInterval(() => {
    try {
      createAutoBackup()
      console.log('تم إنشاء نسخة احتياطية تلقائية')
    } catch (error) {
      console.error('فشل في إنشاء النسخة الاحتياطية التلقائية:', error)
    }
  }, intervalMinutes * 60 * 1000)

  return interval
}

// إحصائيات النسخ الاحتياطي
export function getBackupStats() {
  try {
    const autoBackups = getAutoBackups()
    const lastRestore = localStorage.getItem('lastRestore')
    
    return {
      totalAutoBackups: autoBackups.length,
      latestBackup: autoBackups.length > 0 ? autoBackups[autoBackups.length - 1] : null,
      totalBackupSize: autoBackups.reduce((sum, backup) => sum + backup.size, 0),
      lastRestore: lastRestore ? JSON.parse(lastRestore) : null,
      oldestBackup: autoBackups.length > 0 ? autoBackups[0] : null
    }
  } catch (error) {
    console.error('خطأ في جلب إحصائيات النسخ الاحتياطي:', error)
    return {
      totalAutoBackups: 0,
      latestBackup: null,
      totalBackupSize: 0,
      lastRestore: null,
      oldestBackup: null
    }
  }
}
