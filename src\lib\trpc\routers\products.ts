import { z } from 'zod'
import { createTRPCRouter, publicProcedure } from '../init'
import { db } from '@/lib/db'
import { products } from '@/lib/db/sqlite-schema'
import { eq } from 'drizzle-orm'

const productSchema = z.object({
  name: z.string().min(1, 'اسم المنتج مطلوب'),
  description: z.string().optional(),
  category: z.string().min(1, 'فئة المنتج مطلوبة'),
  price: z.number().min(0, 'السعر يجب أن يكون أكبر من صفر'),
  cost: z.number().min(0, 'التكلفة يجب أن تكون أكبر من صفر'),
  stock: z.number().int().min(0, 'المخزون يجب أن يكون رقم صحيح'),
  minStock: z.number().int().min(0, 'الحد الأدنى للمخزون يجب أن يكون رقم صحيح'),
  unit: z.string().default('قطعة'),
  barcode: z.string().optional(),
  supplier: z.string().optional(),
  location: z.string().optional(),
})

export const productsRouter = createTRPCRouter({
  // جلب جميع المنتجات
  getAll: publicProcedure.query(async () => {
    try {
      return await db.select().from(products)
    } catch (error) {
      console.error('Error fetching products:', error)
      return []
    }
  }),

  // جلب منتج واحد
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      try {
        const result = await db.select().from(products).where(eq(products.id, input.id))
        return result[0] || null
      } catch (error) {
        console.error('Error fetching product:', error)
        return null
      }
    }),

  // إضافة منتج جديد
  create: publicProcedure
    .input(productSchema)
    .mutation(async ({ input }) => {
      try {
        const newProduct = {
          id: Date.now().toString(36) + Math.random().toString(36).substr(2),
          ...input,
          companyId: 'default-company', // سنحتاج لتحديث هذا لاحقاً
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        }
        
        await db.insert(products).values(newProduct)
        return newProduct
      } catch (error) {
        console.error('Error creating product:', error)
        throw new Error('فشل في إضافة المنتج')
      }
    }),

  // تحديث منتج
  update: publicProcedure
    .input(z.object({
      id: z.string(),
      data: productSchema.partial()
    }))
    .mutation(async ({ input }) => {
      try {
        const updatedData = {
          ...input.data,
          updatedAt: Date.now(),
        }
        
        await db.update(products)
          .set(updatedData)
          .where(eq(products.id, input.id))
        
        return { success: true }
      } catch (error) {
        console.error('Error updating product:', error)
        throw new Error('فشل في تحديث المنتج')
      }
    }),

  // حذف منتج
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      try {
        await db.delete(products).where(eq(products.id, input.id))
        return { success: true }
      } catch (error) {
        console.error('Error deleting product:', error)
        throw new Error('فشل في حذف المنتج')
      }
    }),

  // البحث في المنتجات
  search: publicProcedure
    .input(z.object({
      query: z.string(),
      category: z.string().optional(),
    }))
    .query(async ({ input }) => {
      try {
        // سنحتاج لتحسين البحث لاحقاً
        const allProducts = await db.select().from(products)
        return allProducts.filter(product => 
          product.name.toLowerCase().includes(input.query.toLowerCase()) ||
          product.description?.toLowerCase().includes(input.query.toLowerCase()) ||
          (input.category && product.category === input.category)
        )
      } catch (error) {
        console.error('Error searching products:', error)
        return []
      }
    }),

  // إحصائيات المنتجات
  getStats: publicProcedure.query(async () => {
    try {
      const allProducts = await db.select().from(products)
      
      return {
        totalProducts: allProducts.length,
        availableProducts: allProducts.filter(p => p.stock > p.minStock).length,
        lowStockProducts: allProducts.filter(p => p.stock > 0 && p.stock <= p.minStock).length,
        outOfStockProducts: allProducts.filter(p => p.stock === 0).length,
        totalValue: allProducts.reduce((sum, p) => sum + (p.stock * p.cost), 0),
        categories: [...new Set(allProducts.map(p => p.category))],
      }
    } catch (error) {
      console.error('Error fetching product stats:', error)
      return {
        totalProducts: 0,
        availableProducts: 0,
        lowStockProducts: 0,
        outOfStockProducts: 0,
        totalValue: 0,
        categories: [],
      }
    }
  }),
})
