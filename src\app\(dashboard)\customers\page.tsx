'use client'

import { useState, useEffect } from 'react'
import { realDataManager, Customer } from '@/lib/real-storage'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  ArrowLeft, Search, Plus, Users, Phone, Mail, MapPin, 
  Star, Edit, Trash2, Building, CreditCard, TrendingUp,
  UserPlus, X, Save
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

export default function CustomersPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('الكل')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [customers, setCustomers] = useState<Customer[]>([])
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    company: '',
    phone: '',
    email: '',
    address: '',
    type: 'individual' as const,
    creditLimit: 100000
  })

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    realDataManager.initializeData()
    loadData()
  }, [])

  const loadData = () => {
    setCustomers(realDataManager.getCustomers())
  }

  // تصفية العملاء
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm) ||
                         customer.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'الكل' || customer.status === selectedStatus
    return matchesSearch && matchesStatus
  })

  // إحصائيات العملاء
  const stats = {
    totalCustomers: customers.length,
    activeCustomers: customers.filter(c => c.status === 'active').length,
    totalDebt: customers.reduce((sum, c) => sum + c.currentDebt, 0),
    totalSales: customers.reduce((sum, c) => sum + c.totalPurchases, 0)
  }

  const getCustomerStatus = (customer: Customer) => {
    if (customer.totalPurchases > 100000) return 'عميل مميز'
    if (customer.totalPurchases > 50000) return 'عميل عادي'
    return 'عميل جديد'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'عميل مميز': return 'bg-yellow-100 text-yellow-800'
      case 'عميل عادي': return 'bg-blue-100 text-blue-800'
      case 'عميل جديد': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // إضافة عميل جديد
  const addNewCustomer = () => {
    if (!newCustomer.name || !newCustomer.phone) return

    const customer = realDataManager.addCustomer({
      ...newCustomer,
      status: 'active',
      currentDebt: 0,
      totalPurchases: 0
    })

    loadData()
    setNewCustomer({
      name: '',
      company: '',
      phone: '',
      email: '',
      address: '',
      type: 'individual',
      creditLimit: 100000
    })
    setShowAddModal(false)
  }

  // تحديث عميل
  const updateCustomer = () => {
    if (!selectedCustomer) return

    realDataManager.updateCustomer(selectedCustomer.id, {
      name: selectedCustomer.name,
      company: selectedCustomer.company,
      phone: selectedCustomer.phone,
      email: selectedCustomer.email,
      address: selectedCustomer.address,
      type: selectedCustomer.type,
      creditLimit: selectedCustomer.creditLimit
    })

    loadData()
    setShowEditModal(false)
    setSelectedCustomer(null)
  }

  const statuses = ['الكل', 'active', 'inactive']

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
                <ArrowLeft className="h-4 w-4 ml-2" />
                العودة
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">إدارة العملاء</h1>
                <p className="text-sm text-gray-600">إدارة قاعدة بيانات العملاء والمبيعات</p>
              </div>
            </div>
            
            <Button onClick={() => setShowAddModal(true)} className="flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              إضافة عميل جديد
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* إحصائيات العملاء */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">إجمالي العملاء</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalCustomers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Star className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">العملاء النشطين</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeCustomers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <CreditCard className="h-6 w-6 text-red-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">إجمالي الديون</p>
                  <p className="text-2xl font-bold text-red-600">{formatCurrency(stats.totalDebt)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalSales)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* البحث والفلاتر */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في العملاء..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              
              <div className="flex gap-2">
                {statuses.map(status => (
                  <Button
                    key={status}
                    variant={selectedStatus === status ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus(status)}
                  >
                    {status === 'active' ? 'نشط' : status === 'inactive' ? 'غير نشط' : status}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول العملاء */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة العملاء ({filteredCustomers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-right py-3 px-4 font-medium text-gray-900">العميل</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">معلومات الاتصال</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">الحالة</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">إجمالي المشتريات</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">الدين الحالي</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCustomers.map((customer) => (
                    <tr key={customer.id} className="border-b hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{customer.name}</div>
                          {customer.company && (
                            <div className="text-sm text-gray-600 flex items-center gap-1">
                              <Building className="h-3 w-3" />
                              {customer.company}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="space-y-1">
                          <div className="text-sm flex items-center gap-1">
                            <Phone className="h-3 w-3 text-gray-400" />
                            {customer.phone}
                          </div>
                          {customer.email && (
                            <div className="text-sm flex items-center gap-1">
                              <Mail className="h-3 w-3 text-gray-400" />
                              {customer.email}
                            </div>
                          )}
                          {customer.address && (
                            <div className="text-sm flex items-center gap-1">
                              <MapPin className="h-3 w-3 text-gray-400" />
                              {customer.address}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <Badge className={getStatusColor(getCustomerStatus(customer))}>
                          {getCustomerStatus(customer)}
                        </Badge>
                      </td>
                      <td className="py-4 px-4">
                        <div className="font-medium text-green-600">
                          {formatCurrency(customer.totalPurchases)}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`font-medium ${customer.currentDebt > 0 ? 'text-red-600' : 'text-green-600'}`}>
                          {formatCurrency(customer.currentDebt)}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedCustomer(customer)
                              setShowEditModal(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {filteredCustomers.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد عملاء مطابقين للبحث
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* مودال إضافة عميل */}
      {showAddModal && (
        <div className="fixed inset-0 z-50">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowAddModal(false)} />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl w-full max-w-md mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">إضافة عميل جديد</h2>
                <Button variant="ghost" size="sm" onClick={() => setShowAddModal(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">اسم العميل *</label>
                  <Input
                    placeholder="أدخل اسم العميل"
                    value={newCustomer.name}
                    onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">رقم الهاتف *</label>
                  <Input
                    placeholder="أدخل رقم الهاتف"
                    value={newCustomer.phone}
                    onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">اسم الشركة</label>
                  <Input
                    placeholder="أدخل اسم الشركة (اختياري)"
                    value={newCustomer.company}
                    onChange={(e) => setNewCustomer({...newCustomer, company: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">البريد الإلكتروني</label>
                  <Input
                    type="email"
                    placeholder="أدخل البريد الإلكتروني (اختياري)"
                    value={newCustomer.email}
                    onChange={(e) => setNewCustomer({...newCustomer, email: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">العنوان</label>
                  <Input
                    placeholder="أدخل العنوان (اختياري)"
                    value={newCustomer.address}
                    onChange={(e) => setNewCustomer({...newCustomer, address: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">حد الائتمان</label>
                  <Input
                    type="number"
                    placeholder="حد الائتمان بالريال اليمني"
                    value={newCustomer.creditLimit}
                    onChange={(e) => setNewCustomer({...newCustomer, creditLimit: parseInt(e.target.value) || 0})}
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    className="flex-1"
                    onClick={addNewCustomer}
                    disabled={!newCustomer.name || !newCustomer.phone}
                  >
                    <Save className="h-4 w-4 ml-2" />
                    حفظ العميل
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowAddModal(false)}
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* مودال تعديل عميل */}
      {showEditModal && selectedCustomer && (
        <div className="fixed inset-0 z-50">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowEditModal(false)} />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl w-full max-w-md mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">تعديل بيانات العميل</h2>
                <Button variant="ghost" size="sm" onClick={() => setShowEditModal(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">اسم العميل *</label>
                  <Input
                    placeholder="أدخل اسم العميل"
                    value={selectedCustomer.name}
                    onChange={(e) => setSelectedCustomer({...selectedCustomer, name: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">رقم الهاتف *</label>
                  <Input
                    placeholder="أدخل رقم الهاتف"
                    value={selectedCustomer.phone}
                    onChange={(e) => setSelectedCustomer({...selectedCustomer, phone: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">اسم الشركة</label>
                  <Input
                    placeholder="أدخل اسم الشركة (اختياري)"
                    value={selectedCustomer.company || ''}
                    onChange={(e) => setSelectedCustomer({...selectedCustomer, company: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">البريد الإلكتروني</label>
                  <Input
                    type="email"
                    placeholder="أدخل البريد الإلكتروني (اختياري)"
                    value={selectedCustomer.email || ''}
                    onChange={(e) => setSelectedCustomer({...selectedCustomer, email: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">العنوان</label>
                  <Input
                    placeholder="أدخل العنوان (اختياري)"
                    value={selectedCustomer.address || ''}
                    onChange={(e) => setSelectedCustomer({...selectedCustomer, address: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">حد الائتمان</label>
                  <Input
                    type="number"
                    placeholder="حد الائتمان بالريال اليمني"
                    value={selectedCustomer.creditLimit}
                    onChange={(e) => setSelectedCustomer({...selectedCustomer, creditLimit: parseInt(e.target.value) || 0})}
                  />
                </div>

                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-sm text-gray-600 space-y-1">
                    <div className="flex justify-between">
                      <span>إجمالي المشتريات:</span>
                      <span className="font-medium text-green-600">{formatCurrency(selectedCustomer.totalPurchases)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>الدين الحالي:</span>
                      <span className={`font-medium ${selectedCustomer.currentDebt > 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {formatCurrency(selectedCustomer.currentDebt)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    className="flex-1"
                    onClick={updateCustomer}
                    disabled={!selectedCustomer.name || !selectedCustomer.phone}
                  >
                    <Save className="h-4 w-4 ml-2" />
                    حفظ التغييرات
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowEditModal(false)}
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
