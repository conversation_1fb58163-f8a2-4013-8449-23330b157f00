'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface User {
  id: string
  firstName: string
  lastName: string
  email: string
}

interface AuthContextType {
  isSignedIn: boolean
  user: User | null
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, firstName: string, lastName: string) => Promise<void>
  signOut: () => void
}

const AuthContext = createContext<AuthContextType | null>(null)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isSignedIn, setIsSignedIn] = useState(false)
  const [user, setUser] = useState<User | null>(null)

  useEffect(() => {
    // التحقق من وجود مستخدم محفوظ
    const savedUser = localStorage.getItem('fallback-user')
    if (savedUser) {
      try {
        const parsedUser = JSON.parse(savedUser)
        setUser(parsedUser)
        setIsSignedIn(true)
      } catch (error) {
        console.error('خطأ في تحليل بيانات المستخدم:', error)
        localStorage.removeItem('fallback-user')
      }
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    // محاكاة تسجيل الدخول
    const mockUser: User = {
      id: 'user_1',
      firstName: 'مستخدم',
      lastName: 'تجريبي',
      email: email
    }
    
    setUser(mockUser)
    setIsSignedIn(true)
    localStorage.setItem('fallback-user', JSON.stringify(mockUser))
  }

  const signUp = async (email: string, password: string, firstName: string, lastName: string) => {
    // محاكاة التسجيل
    const mockUser: User = {
      id: 'user_' + Date.now(),
      firstName,
      lastName,
      email
    }
    
    setUser(mockUser)
    setIsSignedIn(true)
    localStorage.setItem('fallback-user', JSON.stringify(mockUser))
  }

  const signOut = () => {
    setUser(null)
    setIsSignedIn(false)
    localStorage.removeItem('fallback-user')
  }

  return (
    <AuthContext.Provider value={{ isSignedIn, user, signIn, signUp, signOut }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// مكونات احتياطية للمصادقة
export function FallbackSignInButton({ children, mode = 'redirect' }: { children: ReactNode, mode?: string }) {
  const { signIn } = useAuth()
  
  const handleClick = () => {
    if (mode === 'modal') {
      // محاكاة modal
      const email = prompt('البريد الإلكتروني:')
      const password = prompt('كلمة المرور:')
      if (email && password) {
        signIn(email, password)
      }
    } else {
      // إعادة توجيه لصفحة تسجيل الدخول
      window.location.href = '/sign-in'
    }
  }
  
  return <div onClick={handleClick}>{children}</div>
}

export function FallbackSignUpButton({ children, mode = 'redirect' }: { children: ReactNode, mode?: string }) {
  const { signUp } = useAuth()
  
  const handleClick = () => {
    if (mode === 'modal') {
      // محاكاة modal
      const firstName = prompt('الاسم الأول:')
      const lastName = prompt('الاسم الأخير:')
      const email = prompt('البريد الإلكتروني:')
      const password = prompt('كلمة المرور:')
      if (firstName && lastName && email && password) {
        signUp(email, password, firstName, lastName)
      }
    } else {
      // إعادة توجيه لصفحة التسجيل
      window.location.href = '/sign-up'
    }
  }
  
  return <div onClick={handleClick}>{children}</div>
}

export function FallbackUserButton({ afterSignOutUrl = '/' }: { afterSignOutUrl?: string }) {
  const { user, signOut } = useAuth()
  
  const handleSignOut = () => {
    signOut()
    window.location.href = afterSignOutUrl
  }
  
  if (!user) return null
  
  return (
    <div className="flex items-center gap-2">
      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">
        {user.firstName[0]}
      </div>
      <button onClick={handleSignOut} className="text-sm text-gray-600 hover:text-gray-800">
        تسجيل الخروج
      </button>
    </div>
  )
}
