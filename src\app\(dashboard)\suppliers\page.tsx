'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  ArrowLeft, Search, Plus, Edit, Trash2, Phone,
  Mail, MapPin, Building, Star, Package,
  Eye, Download, Filter, TrendingUp, AlertTriangle, RefreshCw, X, Save, Users
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { realDataManager, Supplier } from '@/lib/real-storage'
import { toast } from '@/hooks/use-toast'

export default function SuppliersPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('الكل')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null)
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [mounted, setMounted] = useState(false)
  const [newSupplier, setNewSupplier] = useState({
    name: '',
    company: '',
    phone: '',
    email: '',
    address: '',
    taxNumber: '',
    status: 'active' as 'active' | 'inactive'
  })

  useEffect(() => {
    setMounted(true)
    realDataManager.initializeData()
    loadSuppliers()
  }, [])

  const loadSuppliers = () => {
    const suppliersData = realDataManager.getSuppliers()
    setSuppliers(suppliersData)
  }

  const statuses = ['الكل', 'نشط', 'غير نشط']

  // تصفية الموردين
  const filteredSuppliers = suppliers.filter(supplier => {
    const matchesSearch = supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplier.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplier.phone.includes(searchTerm) ||
                         supplier.email?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'الكل' ||
                         (selectedStatus === 'نشط' && supplier.status === 'active') ||
                         (selectedStatus === 'غير نشط' && supplier.status === 'inactive')
    return matchesSearch && matchesStatus
  })

  // إحصائيات الموردين
  const stats = {
    totalSuppliers: suppliers.length,
    activeSuppliers: suppliers.filter(s => s.status === 'active').length,
    inactiveSuppliers: suppliers.filter(s => s.status === 'inactive').length,
    totalPurchases: suppliers.reduce((sum, s) => sum + s.totalPurchases, 0)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case 'inactive':
        return <Badge className="bg-red-100 text-red-800">غير نشط</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // إضافة مورد جديد
  const addNewSupplier = () => {
    if (!newSupplier.name || !newSupplier.phone) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم المورد ورقم الهاتف",
        variant: "destructive"
      })
      return
    }

    try {
      const supplier = realDataManager.addSupplier(newSupplier)
      loadSuppliers()
      setNewSupplier({
        name: '',
        company: '',
        phone: '',
        email: '',
        address: '',
        taxNumber: '',
        status: 'active'
      })
      setShowAddModal(false)

      toast({
        title: "تم بنجاح",
        description: "تم إضافة المورد الجديد"
      })
    } catch (error) {
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء إضافة المورد",
        variant: "destructive"
      })
    }
  }

  // تحديث مورد
  const updateSupplier = () => {
    if (!selectedSupplier) return

    try {
      realDataManager.updateSupplier(selectedSupplier.id, {
        name: selectedSupplier.name,
        company: selectedSupplier.company,
        phone: selectedSupplier.phone,
        email: selectedSupplier.email,
        address: selectedSupplier.address,
        taxNumber: selectedSupplier.taxNumber,
        status: selectedSupplier.status
      })

      loadSuppliers()
      setShowEditModal(false)
      setSelectedSupplier(null)

      toast({
        title: "تم بنجاح",
        description: "تم تحديث بيانات المورد"
      })
    } catch (error) {
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تحديث المورد",
        variant: "destructive"
      })
    }
  }

  // حذف مورد
  const deleteSupplier = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
      try {
        realDataManager.deleteSupplier(id)
        loadSuppliers()

        toast({
          title: "تم بنجاح",
          description: "تم حذف المورد"
        })
      } catch (error) {
        toast({
          title: "خطأ",
          description: "حدث خطأ أثناء حذف المورد",
          variant: "destructive"
        })
      }
    }
  }



  if (!mounted) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col" dir="rtl">
      {/* Header */}
      <div className="bg-white shadow-sm border-b p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الموردين</h1>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              تصدير
            </Button>
            <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  إضافة مورد
                </Button>
              </DialogTrigger>
            </Dialog>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="bg-white border-b p-4 flex-shrink-0">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي الموردين</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.totalSuppliers}</p>
                </div>
                <Building className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">الموردون النشطون</p>
                  <p className="text-2xl font-bold text-green-600">{stats.activeSuppliers}</p>
                </div>
                <Package className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">الموردون غير النشطون</p>
                  <p className="text-2xl font-bold text-red-600">{stats.inactiveSuppliers}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي المشتريات</p>
                  <p className="text-2xl font-bold text-purple-600">{formatCurrency(stats.totalPurchases)}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white border-b p-4 flex-shrink-0">
        <div className="flex items-center gap-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="البحث بالاسم أو الفئة أو الهاتف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>

          {/* Status Filter */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">الحالة:</span>
            <div className="flex gap-1">
              {statuses.map(status => (
                <Button
                  key={status}
                  variant={selectedStatus === status ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedStatus(status)}
                >
                  {status}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Suppliers Table */}
      <div className="flex-1 overflow-auto p-4">
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="text-right p-4 font-medium text-gray-700">المورد</th>
                    <th className="text-right p-4 font-medium text-gray-700">معلومات الاتصال</th>
                    <th className="text-right p-4 font-medium text-gray-700">العنوان</th>
                    <th className="text-right p-4 font-medium text-gray-700">إجمالي المشتريات</th>
                    <th className="text-right p-4 font-medium text-gray-700">الحالة</th>
                    <th className="text-right p-4 font-medium text-gray-700">آخر شراء</th>
                    <th className="text-right p-4 font-medium text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredSuppliers.map((supplier) => (
                    <tr key={supplier.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div>
                          <div className="font-medium">{supplier.name}</div>
                          {supplier.company && (
                            <div className="text-sm text-gray-600">{supplier.company}</div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2 text-sm">
                            <Phone className="h-3 w-3" />
                            {supplier.phone}
                          </div>
                          {supplier.email && (
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Mail className="h-3 w-3" />
                              {supplier.email}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-600">
                          {supplier.address || 'غير محدد'}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="font-medium text-green-600">
                          {formatCurrency(supplier.totalPurchases)}
                        </div>
                      </td>
                      <td className="p-4">
                        {getStatusBadge(supplier.status)}
                      </td>
                      <td className="p-4 text-gray-600">
                        {supplier.lastPurchase ?
                          new Date(supplier.lastPurchase).toLocaleDateString('ar-SA') :
                          'لا يوجد'
                        }
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedSupplier(supplier)
                              setShowEditModal(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600"
                            onClick={() => deleteSupplier(supplier.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* مودال إضافة مورد */}
      <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>إضافة مورد جديد</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>اسم المورد *</Label>
              <Input
                value={newSupplier.name}
                onChange={(e) => setNewSupplier({...newSupplier, name: e.target.value})}
                placeholder="أدخل اسم المورد"
              />
            </div>
            <div>
              <Label>اسم الشركة</Label>
              <Input
                value={newSupplier.company}
                onChange={(e) => setNewSupplier({...newSupplier, company: e.target.value})}
                placeholder="أدخل اسم الشركة"
              />
            </div>
            <div>
              <Label>رقم الهاتف *</Label>
              <Input
                value={newSupplier.phone}
                onChange={(e) => setNewSupplier({...newSupplier, phone: e.target.value})}
                placeholder="أدخل رقم الهاتف"
              />
            </div>
            <div>
              <Label>البريد الإلكتروني</Label>
              <Input
                type="email"
                value={newSupplier.email}
                onChange={(e) => setNewSupplier({...newSupplier, email: e.target.value})}
                placeholder="أدخل البريد الإلكتروني"
              />
            </div>
            <div className="col-span-2">
              <Label>العنوان</Label>
              <Input
                value={newSupplier.address}
                onChange={(e) => setNewSupplier({...newSupplier, address: e.target.value})}
                placeholder="أدخل العنوان"
              />
            </div>
            <div>
              <Label>الرقم الضريبي</Label>
              <Input
                value={newSupplier.taxNumber}
                onChange={(e) => setNewSupplier({...newSupplier, taxNumber: e.target.value})}
                placeholder="أدخل الرقم الضريبي"
              />
            </div>
            <div>
              <Label>الحالة</Label>
              <Select value={newSupplier.status} onValueChange={(value: 'active' | 'inactive') => setNewSupplier({...newSupplier, status: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">نشط</SelectItem>
                  <SelectItem value="inactive">غير نشط</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex gap-3 pt-4">
            <Button onClick={addNewSupplier} className="flex-1">
              <Save className="h-4 w-4 ml-2" />
              حفظ المورد
            </Button>
            <Button variant="outline" onClick={() => setShowAddModal(false)}>
              إلغاء
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* مودال تعديل مورد */}
      {showEditModal && selectedSupplier && (
        <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>تعديل بيانات المورد</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>اسم المورد *</Label>
                <Input
                  value={selectedSupplier.name}
                  onChange={(e) => setSelectedSupplier({...selectedSupplier, name: e.target.value})}
                  placeholder="أدخل اسم المورد"
                />
              </div>
              <div>
                <Label>اسم الشركة</Label>
                <Input
                  value={selectedSupplier.company || ''}
                  onChange={(e) => setSelectedSupplier({...selectedSupplier, company: e.target.value})}
                  placeholder="أدخل اسم الشركة"
                />
              </div>
              <div>
                <Label>رقم الهاتف *</Label>
                <Input
                  value={selectedSupplier.phone}
                  onChange={(e) => setSelectedSupplier({...selectedSupplier, phone: e.target.value})}
                  placeholder="أدخل رقم الهاتف"
                />
              </div>
              <div>
                <Label>البريد الإلكتروني</Label>
                <Input
                  type="email"
                  value={selectedSupplier.email || ''}
                  onChange={(e) => setSelectedSupplier({...selectedSupplier, email: e.target.value})}
                  placeholder="أدخل البريد الإلكتروني"
                />
              </div>
              <div className="col-span-2">
                <Label>العنوان</Label>
                <Input
                  value={selectedSupplier.address || ''}
                  onChange={(e) => setSelectedSupplier({...selectedSupplier, address: e.target.value})}
                  placeholder="أدخل العنوان"
                />
              </div>
              <div>
                <Label>الرقم الضريبي</Label>
                <Input
                  value={selectedSupplier.taxNumber || ''}
                  onChange={(e) => setSelectedSupplier({...selectedSupplier, taxNumber: e.target.value})}
                  placeholder="أدخل الرقم الضريبي"
                />
              </div>
              <div>
                <Label>الحالة</Label>
                <Select value={selectedSupplier.status} onValueChange={(value: 'active' | 'inactive') => setSelectedSupplier({...selectedSupplier, status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">نشط</SelectItem>
                    <SelectItem value="inactive">غير نشط</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex gap-3 pt-4">
              <Button onClick={updateSupplier} className="flex-1">
                <Save className="h-4 w-4 ml-2" />
                حفظ التغييرات
              </Button>
              <Button variant="outline" onClick={() => setShowEditModal(false)}>
                إلغاء
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
