import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// الصفحات المحمية
const protectedRoutes = [
  '/dashboard',
  '/products',
  '/customers',
  '/suppliers',
  '/pos',
  '/invoices',
  '/inventory',
  '/returns',
  '/payments',
  '/expenses',
  '/reports',
  '/settings',
  '/users'
]

// الصفحات العامة
const publicRoutes = [
  '/',
  '/sign-in',
  '/sign-up',
  '/fallback-signin'
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // السماح للملفات الثابتة والـ API
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/static') ||
    pathname.includes('.') ||
    pathname.startsWith('/icons') ||
    pathname.startsWith('/manifest') ||
    pathname.startsWith('/sw.js')
  ) {
    return NextResponse.next()
  }

  // للتطوير: السماح بالوصول لجميع الصفحات بدون مصادقة
  // يمكن تفعيل المصادقة لاحقاً عند الحاجة
  return NextResponse.next()

  /*
  // كود المصادقة (معطل مؤقتاً للتطوير)
  const fallbackUser = request.cookies.get('fallback-user')?.value
  const isAuthenticated = !!fallbackUser

  if (protectedRoutes.some(route => pathname.startsWith(route)) && !isAuthenticated) {
    return NextResponse.redirect(new URL('/fallback-signin', request.url))
  }

  if (isAuthenticated && (pathname === '/sign-in' || pathname === '/sign-up' || pathname === '/fallback-signin')) {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  if (isAuthenticated && pathname === '/') {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  return NextResponse.next()
  */
}

export const config = {
  matcher: ['/((?!.+\\.[\\w]+$|_next).*)', '/', '/(api|trpc)(.*)']
}
