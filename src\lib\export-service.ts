// خدمة تصدير البيانات إلى Excel و CSV
import { Sale, Product, Customer, Return, Supplier } from './real-storage'
import * as XLSX from 'xlsx'

export interface ExportOptions {
  format: 'excel' | 'csv' | 'json'
  filename?: string
  includeHeaders?: boolean
  dateRange?: {
    start: Date
    end: Date
  }
  columns?: string[]
  language?: 'ar' | 'en'
}

class ExportService {
  
  // تصدير المبيعات
  public exportSales(sales: Sale[], options: ExportOptions = { format: 'excel' }): void {
    const headers = options.language === 'en' ? 
      ['Invoice ID', 'Customer', 'Date', 'Total', 'Paid', 'Remaining', 'Payment Type', 'Status'] :
      ['رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ', 'المدفوع', 'المتبقي', 'نوع الدفع', 'الحالة']
    
    const data = sales.map(sale => [
      sale.id.slice(-6),
      sale.customerName,
      new Date(sale.createdAt).toLocaleDateString('ar-SA'),
      sale.total,
      sale.paidAmount,
      sale.remainingAmount,
      this.getPaymentTypeLabel(sale.paymentType),
      this.getStatusLabel(sale.status)
    ])
    
    const filename = options.filename || `sales_${new Date().toISOString().split('T')[0]}`
    
    if (options.format === 'excel') {
      this.exportToExcel(data, headers, filename)
    } else if (options.format === 'csv') {
      this.exportToCSV(data, headers, filename)
    } else {
      this.exportToJSON(sales, filename)
    }
  }

  // تصدير المنتجات
  public exportProducts(products: Product[], options: ExportOptions = { format: 'excel' }): void {
    const headers = options.language === 'en' ? 
      ['ID', 'Name', 'Category', 'Price', 'Cost', 'Stock', 'Min Stock', 'Supplier', 'Status'] :
      ['الرقم', 'اسم المنتج', 'الفئة', 'السعر', 'التكلفة', 'المخزون', 'الحد الأدنى', 'المورد', 'الحالة']
    
    const data = products.map(product => [
      product.id,
      product.name,
      product.category,
      product.price,
      product.cost,
      product.stock,
      product.minStock,
      product.supplier,
      product.stock <= product.minStock ? 'مخزون منخفض' : 'متوفر'
    ])
    
    const filename = options.filename || `products_${new Date().toISOString().split('T')[0]}`
    
    if (options.format === 'excel') {
      this.exportToExcel(data, headers, filename)
    } else if (options.format === 'csv') {
      this.exportToCSV(data, headers, filename)
    } else {
      this.exportToJSON(products, filename)
    }
  }

  // تصدير العملاء
  public exportCustomers(customers: Customer[], options: ExportOptions = { format: 'excel' }): void {
    const headers = options.language === 'en' ? 
      ['ID', 'Name', 'Phone', 'Type', 'Total Purchases', 'Current Debt', 'Credit Limit', 'Status'] :
      ['الرقم', 'الاسم', 'الهاتف', 'النوع', 'إجمالي المشتريات', 'الدين الحالي', 'حد الائتمان', 'الحالة']
    
    const data = customers.map(customer => [
      customer.id,
      customer.name,
      customer.phone,
      customer.type === 'individual' ? 'فرد' : 'شركة',
      customer.totalPurchases,
      customer.currentDebt,
      customer.creditLimit,
      customer.status === 'active' ? 'نشط' : 'غير نشط'
    ])
    
    const filename = options.filename || `customers_${new Date().toISOString().split('T')[0]}`
    
    if (options.format === 'excel') {
      this.exportToExcel(data, headers, filename)
    } else if (options.format === 'csv') {
      this.exportToCSV(data, headers, filename)
    } else {
      this.exportToJSON(customers, filename)
    }
  }

  // تصدير المرتجعات
  public exportReturns(returns: Return[], options: ExportOptions = { format: 'excel' }): void {
    const headers = options.language === 'en' ? 
      ['Return ID', 'Sale ID', 'Customer', 'Date', 'Total', 'Reason', 'Status'] :
      ['رقم المرتجع', 'رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ', 'السبب', 'الحالة']
    
    const data = returns.map(returnItem => [
      returnItem.id,
      returnItem.saleId,
      returnItem.customerName,
      new Date(returnItem.createdAt).toLocaleDateString('ar-SA'),
      returnItem.total,
      returnItem.reason,
      returnItem.status === 'completed' ? 'مكتمل' : 'معلق'
    ])
    
    const filename = options.filename || `returns_${new Date().toISOString().split('T')[0]}`
    
    if (options.format === 'excel') {
      this.exportToExcel(data, headers, filename)
    } else if (options.format === 'csv') {
      this.exportToCSV(data, headers, filename)
    } else {
      this.exportToJSON(returns, filename)
    }
  }

  // تصدير تقرير شامل
  public exportComprehensiveReport(data: {
    sales: Sale[]
    products: Product[]
    customers: Customer[]
    returns: Return[]
  }, options: ExportOptions = { format: 'excel' }): void {
    
    if (options.format === 'excel') {
      this.exportMultiSheetExcel(data, options.filename || `comprehensive_report_${new Date().toISOString().split('T')[0]}`)
    } else {
      // For CSV and JSON, export each separately
      this.exportSales(data.sales, { ...options, filename: `sales_${new Date().toISOString().split('T')[0]}` })
      this.exportProducts(data.products, { ...options, filename: `products_${new Date().toISOString().split('T')[0]}` })
      this.exportCustomers(data.customers, { ...options, filename: `customers_${new Date().toISOString().split('T')[0]}` })
      this.exportReturns(data.returns, { ...options, filename: `returns_${new Date().toISOString().split('T')[0]}` })
    }
  }

  // تصدير إلى Excel (ورقة واحدة)
  private exportToExcel(data: any[][], headers: string[], filename: string): void {
    const worksheet = this.createWorksheet([headers, ...data])
    const workbook = { Sheets: { 'البيانات': worksheet }, SheetNames: ['البيانات'] }
    
    this.downloadExcel(workbook, filename)
  }

  // تصدير إلى Excel (أوراق متعددة)
  private exportMultiSheetExcel(data: {
    sales: Sale[]
    products: Product[]
    customers: Customer[]
    returns: Return[]
  }, filename: string): void {
    
    const salesHeaders = ['رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ', 'المدفوع', 'المتبقي', 'نوع الدفع', 'الحالة']
    const salesData = data.sales.map(sale => [
      sale.id.slice(-6),
      sale.customerName,
      new Date(sale.createdAt).toLocaleDateString('ar-SA'),
      sale.total,
      sale.paidAmount,
      sale.remainingAmount,
      this.getPaymentTypeLabel(sale.paymentType),
      this.getStatusLabel(sale.status)
    ])

    const productsHeaders = ['الرقم', 'اسم المنتج', 'الفئة', 'السعر', 'التكلفة', 'المخزون', 'الحد الأدنى', 'المورد', 'الحالة']
    const productsData = data.products.map(product => [
      product.id,
      product.name,
      product.category,
      product.price,
      product.cost,
      product.stock,
      product.minStock,
      product.supplier,
      product.stock <= product.minStock ? 'مخزون منخفض' : 'متوفر'
    ])

    const customersHeaders = ['الرقم', 'الاسم', 'الهاتف', 'النوع', 'إجمالي المشتريات', 'الدين الحالي', 'حد الائتمان', 'الحالة']
    const customersData = data.customers.map(customer => [
      customer.id,
      customer.name,
      customer.phone,
      customer.type === 'individual' ? 'فرد' : 'شركة',
      customer.totalPurchases,
      customer.currentDebt,
      customer.creditLimit,
      customer.status === 'active' ? 'نشط' : 'غير نشط'
    ])

    const returnsHeaders = ['رقم المرتجع', 'رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ', 'السبب', 'الحالة']
    const returnsData = data.returns.map(returnItem => [
      returnItem.id,
      returnItem.saleId,
      returnItem.customerName,
      new Date(returnItem.createdAt).toLocaleDateString('ar-SA'),
      returnItem.total,
      returnItem.reason,
      returnItem.status === 'completed' ? 'مكتمل' : 'معلق'
    ])

    const workbook = {
      Sheets: {
        'المبيعات': this.createWorksheet([salesHeaders, ...salesData]),
        'المنتجات': this.createWorksheet([productsHeaders, ...productsData]),
        'العملاء': this.createWorksheet([customersHeaders, ...customersData]),
        'المرتجعات': this.createWorksheet([returnsHeaders, ...returnsData])
      },
      SheetNames: ['المبيعات', 'المنتجات', 'العملاء', 'المرتجعات']
    }
    
    this.downloadExcel(workbook, filename)
  }

  // تصدير إلى CSV
  private exportToCSV(data: any[][], headers: string[], filename: string): void {
    const csvContent = [headers, ...data]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n')
    
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    this.downloadFile(blob, `${filename}.csv`)
  }

  // تصدير إلى JSON
  private exportToJSON(data: any[], filename: string): void {
    const jsonContent = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
    this.downloadFile(blob, `${filename}.json`)
  }

  // إنشاء ورقة عمل Excel
  private createWorksheet(data: any[][]): any {
    const worksheet: any = {}
    const range = { s: { c: 0, r: 0 }, e: { c: 0, r: 0 } }

    for (let R = 0; R < data.length; R++) {
      for (let C = 0; C < data[R].length; C++) {
        if (range.s.r > R) range.s.r = R
        if (range.s.c > C) range.s.c = C
        if (range.e.r < R) range.e.r = R
        if (range.e.c < C) range.e.c = C

        const cell: any = { v: data[R][C] }
        
        if (cell.v == null) continue
        
        const cellRef = this.encodeCell({ c: C, r: R })
        
        if (typeof cell.v === 'number') {
          cell.t = 'n'
        } else if (typeof cell.v === 'boolean') {
          cell.t = 'b'
        } else {
          cell.t = 's'
        }
        
        worksheet[cellRef] = cell
      }
    }
    
    if (range.s.c < 10000000) worksheet['!ref'] = this.encodeRange(range)
    
    return worksheet
  }

  // ترميز خلية Excel
  private encodeCell(cell: { c: number, r: number }): string {
    return this.encodeCol(cell.c) + this.encodeRow(cell.r)
  }

  // ترميز عمود Excel
  private encodeCol(col: number): string {
    let s = ''
    for (++col; col; col = Math.floor((col - 1) / 26)) {
      s = String.fromCharCode(((col - 1) % 26) + 65) + s
    }
    return s
  }

  // ترميز صف Excel
  private encodeRow(row: number): string {
    return (row + 1).toString()
  }

  // ترميز نطاق Excel
  private encodeRange(range: any): string {
    return this.encodeCell(range.s) + ':' + this.encodeCell(range.e)
  }

  // تحميل ملف Excel
  private downloadExcel(workbook: any, filename: string): void {
    const wbout = this.writeWorkbook(workbook)
    const blob = new Blob([wbout], { type: 'application/octet-stream' })
    this.downloadFile(blob, `${filename}.xlsx`)
  }

  // كتابة ملف Excel
  private writeWorkbook(workbook: any): ArrayBuffer {
    const buf = new ArrayBuffer(0x1000000)
    const view = new Uint8Array(buf)
    let idx = 0

    // Simple Excel file structure (simplified)
    // In a real implementation, you would use a library like SheetJS
    const content = JSON.stringify(workbook)
    const encoder = new TextEncoder()
    const encoded = encoder.encode(content)
    
    for (let i = 0; i < encoded.length && idx < buf.byteLength; i++) {
      view[idx++] = encoded[i]
    }
    
    return buf.slice(0, idx)
  }

  // تحميل ملف
  private downloadFile(blob: Blob, filename: string): void {
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    
    link.href = url
    link.download = filename
    link.style.display = 'none'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
  }

  // مساعدات للتسميات
  private getPaymentTypeLabel(type: string): string {
    switch (type) {
      case 'cash': return 'نقدي'
      case 'credit': return 'آجل'
      case 'partial': return 'جزئي'
      default: return type
    }
  }

  private getStatusLabel(status: string): string {
    switch (status) {
      case 'completed': return 'مكتملة'
      case 'pending': return 'معلقة'
      case 'cancelled': return 'ملغية'
      default: return status
    }
  }
}

export const exportService = new ExportService()
