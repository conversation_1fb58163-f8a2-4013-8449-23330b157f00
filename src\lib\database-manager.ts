// نظام إدارة قاعدة البيانات الحقيقية
'use client'

import { db } from './db'
import { products, customers, suppliers, sales, saleItems } from './db/sqlite-schema'
import { eq } from 'drizzle-orm'

// تعريف الأنواع
export interface Product {
  id: string
  name: string
  description?: string
  category: string
  price: number
  cost: number
  unit: string
  stock: number
  minStock: number
  barcode?: string
  supplier?: string
  location?: string
  userId?: string
  createdAt: number
  updatedAt: number
}

export interface Customer {
  id: string
  name: string
  company?: string
  phone: string
  email?: string
  address?: string
  type: 'individual' | 'company'
  status: 'active' | 'inactive'
  creditLimit: number
  currentDebt: number
  totalPurchases: number
  lastPurchase?: number
  notes?: string
  userId?: string
  createdAt: number
  updatedAt: number
}

export interface Sale {
  id: string
  customerId?: string
  customerName: string
  subtotal: number
  discount: number
  tax: number
  total: number
  paymentType: 'cash' | 'credit' | 'partial'
  paidAmount: number
  remainingAmount: number
  status: 'completed' | 'pending' | 'cancelled'
  notes?: string
  items: SaleItem[]
  userId?: string
  createdAt: number
  updatedAt: number
}

export interface SaleItem {
  id: string
  saleId: string
  productId: string
  productName: string
  quantity: number
  unitPrice: number
  total: number
  createdAt: number
}

// فئة إدارة قاعدة البيانات
class DatabaseManager {
  private isClient = typeof window !== 'undefined'

  // المنتجات
  async getProducts(): Promise<Product[]> {
    if (!this.isClient) return []
    
    try {
      const result = await db.select().from(products)
      return result.map(p => ({
        ...p,
        createdAt: p.createdAt ? new Date(p.createdAt).getTime() : Date.now(),
        updatedAt: p.updatedAt ? new Date(p.updatedAt).getTime() : Date.now()
      }))
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error)
      return this.getFallbackProducts()
    }
  }

  async addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {
    if (!this.isClient) throw new Error('غير متاح في الخادم')
    
    try {
      const newProduct = {
        id: Date.now().toString(36) + Math.random().toString(36).substr(2),
        ...product,
        userId: 'user_1', // سيتم تحديثه لاحقاً مع المصادقة
        createdAt: Date.now(),
        updatedAt: Date.now()
      }

      await db.insert(products).values({
        id: newProduct.id,
        name: newProduct.name,
        description: newProduct.description || '',
        category: newProduct.category,
        price: newProduct.price,
        cost: newProduct.cost,
        unit: newProduct.unit,
        stock: newProduct.stock,
        minStock: newProduct.minStock,
        barcode: newProduct.barcode || '',
        supplier: newProduct.supplier || '',
        location: newProduct.location || '',
        userId: newProduct.userId,
        createdAt: newProduct.createdAt,
        updatedAt: newProduct.updatedAt
      })

      return newProduct
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error)
      throw new Error('فشل في إضافة المنتج')
    }
  }

  async updateProduct(id: string, updates: Partial<Product>): Promise<void> {
    if (!this.isClient) throw new Error('غير متاح في الخادم')
    
    try {
      await db.update(products)
        .set({
          ...updates,
          updatedAt: Date.now()
        })
        .where(eq(products.id, id))
    } catch (error) {
      console.error('خطأ في تحديث المنتج:', error)
      throw new Error('فشل في تحديث المنتج')
    }
  }

  async deleteProduct(id: string): Promise<void> {
    if (!this.isClient) throw new Error('غير متاح في الخادم')
    
    try {
      await db.delete(products).where(eq(products.id, id))
    } catch (error) {
      console.error('خطأ في حذف المنتج:', error)
      throw new Error('فشل في حذف المنتج')
    }
  }

  // العملاء
  async getCustomers(): Promise<Customer[]> {
    if (!this.isClient) return []
    
    try {
      const result = await db.select().from(customers)
      return result.map(c => ({
        ...c,
        createdAt: c.createdAt ? new Date(c.createdAt).getTime() : Date.now(),
        updatedAt: c.updatedAt ? new Date(c.updatedAt).getTime() : Date.now()
      }))
    } catch (error) {
      console.error('خطأ في جلب العملاء:', error)
      return this.getFallbackCustomers()
    }
  }

  async addCustomer(customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Promise<Customer> {
    if (!this.isClient) throw new Error('غير متاح في الخادم')
    
    try {
      const newCustomer = {
        id: Date.now().toString(36) + Math.random().toString(36).substr(2),
        ...customer,
        userId: 'user_1',
        createdAt: Date.now(),
        updatedAt: Date.now()
      }

      await db.insert(customers).values({
        id: newCustomer.id,
        name: newCustomer.name,
        company: newCustomer.company || '',
        email: newCustomer.email || '',
        phone: newCustomer.phone,
        address: newCustomer.address || '',
        taxNumber: '',
        type: newCustomer.type,
        status: newCustomer.status,
        creditLimit: newCustomer.creditLimit,
        currentDebt: newCustomer.currentDebt,
        totalPurchases: newCustomer.totalPurchases,
        lastPurchase: newCustomer.lastPurchase || null,
        notes: newCustomer.notes || '',
        userId: newCustomer.userId,
        createdAt: newCustomer.createdAt,
        updatedAt: newCustomer.updatedAt
      })

      return newCustomer
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error)
      throw new Error('فشل في إضافة العميل')
    }
  }

  // المبيعات
  async getSales(): Promise<Sale[]> {
    if (!this.isClient) return []
    
    try {
      const salesResult = await db.select().from(sales)
      const salesWithItems = await Promise.all(
        salesResult.map(async (sale) => {
          const items = await db.select().from(saleItems).where(eq(saleItems.saleId, sale.id))
          return {
            ...sale,
            items: items.map(item => ({
              ...item,
              createdAt: item.createdAt ? new Date(item.createdAt).getTime() : Date.now()
            })),
            createdAt: sale.createdAt ? new Date(sale.createdAt).getTime() : Date.now(),
            updatedAt: sale.updatedAt ? new Date(sale.updatedAt).getTime() : Date.now()
          }
        })
      )
      return salesWithItems
    } catch (error) {
      console.error('خطأ في جلب المبيعات:', error)
      return this.getFallbackSales()
    }
  }

  // البيانات الاحتياطية (localStorage)
  private getFallbackProducts(): Product[] {
    try {
      const stored = localStorage.getItem('products')
      return stored ? JSON.parse(stored) : []
    } catch {
      return []
    }
  }

  private getFallbackCustomers(): Customer[] {
    try {
      const stored = localStorage.getItem('customers')
      return stored ? JSON.parse(stored) : []
    } catch {
      return []
    }
  }

  private getFallbackSales(): Sale[] {
    try {
      const stored = localStorage.getItem('sales')
      return stored ? JSON.parse(stored) : []
    } catch {
      return []
    }
  }

  // تهيئة البيانات
  initializeData() {
    // لا حاجة لتهيئة خاصة مع قاعدة البيانات الحقيقية
    console.log('تم تهيئة مدير قاعدة البيانات')
  }
}

// إنشاء مثيل واحد
export const databaseManager = new DatabaseManager()

// للتوافق مع الكود الموجود
export const realDataManager = {
  getProducts: () => databaseManager.getProducts(),
  getCustomers: () => databaseManager.getCustomers(),
  getSales: () => databaseManager.getSales(),
  getSuppliers: () => databaseManager.getFallbackCustomers(), // مؤقت
  getReturns: () => [],
  getStockMovements: () => [],
  getPayments: () => [],
  getExpenses: () => [],
  initializeData: () => databaseManager.initializeData()
}
