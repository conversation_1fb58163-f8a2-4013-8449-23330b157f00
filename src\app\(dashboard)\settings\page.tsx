'use client'

import { useState } from 'react'
import { Save, Building, User, Bell, Shield, Palette, Globe } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import BackupManager from '@/components/backup/BackupManager'
// import { ThemeToggle, ThemeSelector, CompanyThemeSelector } from '@/lib/theme-provider'
// import { useNotifications } from '@/lib/notifications'

export default function SettingsPage() {
  const [companySettings, setCompanySettings] = useState({
    name: 'شركة التجارة الحديثة للحديد والمعدات',
    email: '<EMAIL>',
    phone: '*********',
    address: 'صنعاء، شارع الزبيري، بجانب السوق المركزي',
    taxNumber: '*********',
    website: 'https://steel-trade.com',
    logo: '',
  })

  const [invoiceSettings, setInvoiceSettings] = useState({
    taxRate: '15',
    currency: 'YER',
    invoicePrefix: 'INV-',
    paymentTerms: '30',
    notes: 'شكراً لك على ثقتك في منتجاتنا من الحديد والمعدات',
  })

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    overdueReminders: true,
    paymentConfirmations: true,
    weeklyReports: true,
    lowStockAlerts: true,
    dailyBackup: true,
  })

  // const { requestPermission, showNotification } = useNotifications()
  const [pushNotificationsEnabled, setPushNotificationsEnabled] = useState(false)

  // تفعيل الإشعارات - مُعطل مؤقتاً
  const handleEnablePushNotifications = async () => {
    alert('ميزة الإشعارات ستكون متاحة في التحديث القادم')
  }

  // اختبار الإشعارات - مُعطل مؤقتاً
  const testNotification = async () => {
    alert('ميزة الإشعارات ستكون متاحة في التحديث القادم')
  }

  const [systemSettings, setSystemSettings] = useState({
    language: 'ar',
    timezone: 'Asia/Riyadh',
    dateFormat: 'dd/mm/yyyy',
    numberFormat: 'arabic',
    autoBackup: true,
    backupFrequency: 'daily',
    maxLoginAttempts: '3',
    sessionTimeout: '60',
  })

  const [inventorySettings, setInventorySettings] = useState({
    lowStockThreshold: '10',
    autoReorderPoint: false,
    trackSerialNumbers: false,
    allowNegativeStock: false,
    defaultUnit: 'طن',
    costMethod: 'fifo',
  })

  const [userSettings, setUserSettings] = useState({
    name: 'أحمد محمد علي',
    email: '<EMAIL>',
    phone: '*********',
    role: 'مدير',
    language: 'ar',
    timezone: 'Asia/Riyadh',
  })

  const handleSaveCompanySettings = () => {
    console.log('Saving company settings:', companySettings)
    // هنا سيتم حفظ إعدادات الشركة
  }

  const handleSaveInvoiceSettings = () => {
    console.log('Saving invoice settings:', invoiceSettings)
    // هنا سيتم حفظ إعدادات الفواتير
  }

  const handleSaveNotificationSettings = () => {
    console.log('Saving notification settings:', notificationSettings)
    // هنا سيتم حفظ إعدادات الإشعارات
  }

  const handleSaveUserSettings = () => {
    console.log('Saving user settings:', userSettings)
    // هنا سيتم حفظ إعدادات المستخدم
  }

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">الإعدادات</h1>
          <p className="text-gray-600 mt-2">إدارة إعدادات النظام والشركة</p>
        </div>

        <Tabs defaultValue="company" className="space-y-6">
          <TabsList className="grid w-full grid-cols-8">
            <TabsTrigger value="company" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              الشركة
            </TabsTrigger>
            <TabsTrigger value="invoices" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              الفواتير
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              الإشعارات
            </TabsTrigger>
            <TabsTrigger value="theme" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              الثيمات
            </TabsTrigger>
            <TabsTrigger value="push" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              إشعارات متقدمة
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              النظام
            </TabsTrigger>
            <TabsTrigger value="inventory" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              المخزون
            </TabsTrigger>
            <TabsTrigger value="backup" className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              النسخ الاحتياطي
            </TabsTrigger>
            <TabsTrigger value="user" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              المستخدم
            </TabsTrigger>
          </TabsList>

          {/* Company Settings */}
          <TabsContent value="company">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  إعدادات الشركة
                </CardTitle>
                <CardDescription>
                  معلومات الشركة الأساسية التي تظهر في الفواتير
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="companyName">اسم الشركة</Label>
                    <Input
                      id="companyName"
                      value={companySettings.name}
                      onChange={(e) => setCompanySettings({...companySettings, name: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="companyEmail">البريد الإلكتروني</Label>
                    <Input
                      id="companyEmail"
                      type="email"
                      value={companySettings.email}
                      onChange={(e) => setCompanySettings({...companySettings, email: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="companyPhone">رقم الهاتف</Label>
                    <Input
                      id="companyPhone"
                      value={companySettings.phone}
                      onChange={(e) => setCompanySettings({...companySettings, phone: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="taxNumber">الرقم الضريبي</Label>
                    <Input
                      id="taxNumber"
                      value={companySettings.taxNumber}
                      onChange={(e) => setCompanySettings({...companySettings, taxNumber: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="website">الموقع الإلكتروني</Label>
                    <Input
                      id="website"
                      value={companySettings.website}
                      onChange={(e) => setCompanySettings({...companySettings, website: e.target.value})}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="address">العنوان</Label>
                  <Textarea
                    id="address"
                    value={companySettings.address}
                    onChange={(e) => setCompanySettings({...companySettings, address: e.target.value})}
                    rows={3}
                  />
                </div>
                <Button onClick={handleSaveCompanySettings}>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ إعدادات الشركة
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Invoice Settings */}
          <TabsContent value="invoices">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  إعدادات الفواتير
                </CardTitle>
                <CardDescription>
                  تخصيص شكل ومحتوى الفواتير
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="taxRate">معدل الضريبة (%)</Label>
                    <Input
                      id="taxRate"
                      type="number"
                      value={invoiceSettings.taxRate}
                      onChange={(e) => setInvoiceSettings({...invoiceSettings, taxRate: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="currency">العملة</Label>
                    <Select value={invoiceSettings.currency} onValueChange={(value) => setInvoiceSettings({...invoiceSettings, currency: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="SAR">ريال سعودي (SAR)</SelectItem>
                        <SelectItem value="USD">دولار أمريكي (USD)</SelectItem>
                        <SelectItem value="EUR">يورو (EUR)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="invoicePrefix">بادئة رقم الفاتورة</Label>
                    <Input
                      id="invoicePrefix"
                      value={invoiceSettings.invoicePrefix}
                      onChange={(e) => setInvoiceSettings({...invoiceSettings, invoicePrefix: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="paymentTerms">مدة الدفع (أيام)</Label>
                    <Input
                      id="paymentTerms"
                      type="number"
                      value={invoiceSettings.paymentTerms}
                      onChange={(e) => setInvoiceSettings({...invoiceSettings, paymentTerms: e.target.value})}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">ملاحظات افتراضية</Label>
                  <Textarea
                    id="notes"
                    value={invoiceSettings.notes}
                    onChange={(e) => setInvoiceSettings({...invoiceSettings, notes: e.target.value})}
                    rows={3}
                  />
                </div>
                <Button onClick={handleSaveInvoiceSettings}>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ إعدادات الفواتير
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notification Settings */}
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  إعدادات الإشعارات
                </CardTitle>
                <CardDescription>
                  تحكم في الإشعارات التي تريد استلامها
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>إشعارات البريد الإلكتروني</Label>
                      <p className="text-sm text-gray-500">استلام إشعارات عبر البريد الإلكتروني</p>
                    </div>
                    <Switch
                      checked={notificationSettings.emailNotifications}
                      onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, emailNotifications: checked})}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>إشعارات الرسائل النصية</Label>
                      <p className="text-sm text-gray-500">استلام إشعارات عبر الرسائل النصية</p>
                    </div>
                    <Switch
                      checked={notificationSettings.smsNotifications}
                      onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, smsNotifications: checked})}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>تذكير الفواتير المتأخرة</Label>
                      <p className="text-sm text-gray-500">إشعارات عند تأخر دفع الفواتير</p>
                    </div>
                    <Switch
                      checked={notificationSettings.overdueReminders}
                      onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, overdueReminders: checked})}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>تأكيد المدفوعات</Label>
                      <p className="text-sm text-gray-500">إشعارات عند استلام المدفوعات</p>
                    </div>
                    <Switch
                      checked={notificationSettings.paymentConfirmations}
                      onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, paymentConfirmations: checked})}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>التقارير الأسبوعية</Label>
                      <p className="text-sm text-gray-500">استلام تقارير أسبوعية عن الأداء</p>
                    </div>
                    <Switch
                      checked={notificationSettings.weeklyReports}
                      onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, weeklyReports: checked})}
                    />
                  </div>
                </div>
                <Button onClick={handleSaveNotificationSettings}>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ إعدادات الإشعارات
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* User Settings */}
          <TabsContent value="user">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  إعدادات المستخدم
                </CardTitle>
                <CardDescription>
                  معلوماتك الشخصية وتفضيلات النظام
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="userName">الاسم</Label>
                    <Input
                      id="userName"
                      value={userSettings.name}
                      onChange={(e) => setUserSettings({...userSettings, name: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="userEmail">البريد الإلكتروني</Label>
                    <Input
                      id="userEmail"
                      type="email"
                      value={userSettings.email}
                      onChange={(e) => setUserSettings({...userSettings, email: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="userPhone">رقم الهاتف</Label>
                    <Input
                      id="userPhone"
                      value={userSettings.phone}
                      onChange={(e) => setUserSettings({...userSettings, phone: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="role">الدور</Label>
                    <Input
                      id="role"
                      value={userSettings.role}
                      disabled
                      className="bg-gray-50"
                    />
                  </div>
                  <div>
                    <Label htmlFor="language">اللغة</Label>
                    <Select value={userSettings.language} onValueChange={(value) => setUserSettings({...userSettings, language: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ar">العربية</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="timezone">المنطقة الزمنية</Label>
                    <Select value={userSettings.timezone} onValueChange={(value) => setUserSettings({...userSettings, timezone: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Asia/Riyadh">الرياض (GMT+3)</SelectItem>
                        <SelectItem value="Asia/Dubai">دبي (GMT+4)</SelectItem>
                        <SelectItem value="Europe/London">لندن (GMT+0)</SelectItem>
                        <SelectItem value="America/New_York">نيويورك (GMT-5)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <Button onClick={handleSaveUserSettings}>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ إعدادات المستخدم
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* إعدادات النظام */}
          <TabsContent value="system" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات النظام العامة</CardTitle>
                <CardDescription>
                  إعدادات اللغة والمنطقة الزمنية والأمان
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="language">اللغة</Label>
                    <Select value={systemSettings.language} onValueChange={(value) => setSystemSettings({...systemSettings, language: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ar">العربية</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timezone">المنطقة الزمنية</Label>
                    <Select value={systemSettings.timezone} onValueChange={(value) => setSystemSettings({...systemSettings, timezone: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Asia/Riyadh">الرياض (GMT+3)</SelectItem>
                        <SelectItem value="Asia/Dubai">دبي (GMT+4)</SelectItem>
                        <SelectItem value="Africa/Cairo">القاهرة (GMT+2)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dateFormat">تنسيق التاريخ</Label>
                    <Select value={systemSettings.dateFormat} onValueChange={(value) => setSystemSettings({...systemSettings, dateFormat: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="dd/mm/yyyy">يوم/شهر/سنة</SelectItem>
                        <SelectItem value="mm/dd/yyyy">شهر/يوم/سنة</SelectItem>
                        <SelectItem value="yyyy-mm-dd">سنة-شهر-يوم</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="sessionTimeout">انتهاء الجلسة (دقيقة)</Label>
                    <Input
                      id="sessionTimeout"
                      value={systemSettings.sessionTimeout}
                      onChange={(e) => setSystemSettings({...systemSettings, sessionTimeout: e.target.value})}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>النسخ الاحتياطي التلقائي</Label>
                      <p className="text-sm text-gray-600">
                        إنشاء نسخة احتياطية تلقائياً من البيانات
                      </p>
                    </div>
                    <Switch
                      checked={systemSettings.autoBackup}
                      onCheckedChange={(checked) => setSystemSettings({...systemSettings, autoBackup: checked})}
                    />
                  </div>
                </div>

                <Button onClick={() => console.log('Saving system settings:', systemSettings)}>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ إعدادات النظام
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* إعدادات المخزون */}
          <TabsContent value="inventory" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات المخزون</CardTitle>
                <CardDescription>
                  إعدادات إدارة المخزون والتنبيهات
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="lowStockThreshold">حد التنبيه للمخزون المنخفض</Label>
                    <Input
                      id="lowStockThreshold"
                      value={inventorySettings.lowStockThreshold}
                      onChange={(e) => setInventorySettings({...inventorySettings, lowStockThreshold: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="defaultUnit">الوحدة الافتراضية</Label>
                    <Select value={inventorySettings.defaultUnit} onValueChange={(value) => setInventorySettings({...inventorySettings, defaultUnit: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="طن">طن</SelectItem>
                        <SelectItem value="كيلو">كيلو</SelectItem>
                        <SelectItem value="قطعة">قطعة</SelectItem>
                        <SelectItem value="متر">متر</SelectItem>
                        <SelectItem value="لوح">لوح</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="costMethod">طريقة حساب التكلفة</Label>
                    <Select value={inventorySettings.costMethod} onValueChange={(value) => setInventorySettings({...inventorySettings, costMethod: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fifo">الوارد أولاً صادر أولاً (FIFO)</SelectItem>
                        <SelectItem value="lifo">الوارد أخيراً صادر أولاً (LIFO)</SelectItem>
                        <SelectItem value="average">المتوسط المرجح</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>السماح بالمخزون السالب</Label>
                      <p className="text-sm text-gray-600">
                        السماح ببيع منتجات غير متوفرة في المخزون
                      </p>
                    </div>
                    <Switch
                      checked={inventorySettings.allowNegativeStock}
                      onCheckedChange={(checked) => setInventorySettings({...inventorySettings, allowNegativeStock: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>تتبع الأرقام التسلسلية</Label>
                      <p className="text-sm text-gray-600">
                        تتبع الأرقام التسلسلية للمنتجات
                      </p>
                    </div>
                    <Switch
                      checked={inventorySettings.trackSerialNumbers}
                      onCheckedChange={(checked) => setInventorySettings({...inventorySettings, trackSerialNumbers: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>إعادة الطلب التلقائي</Label>
                      <p className="text-sm text-gray-600">
                        إنشاء طلبات شراء تلقائياً عند انخفاض المخزون
                      </p>
                    </div>
                    <Switch
                      checked={inventorySettings.autoReorderPoint}
                      onCheckedChange={(checked) => setInventorySettings({...inventorySettings, autoReorderPoint: checked})}
                    />
                  </div>
                </div>

                <Button onClick={() => console.log('Saving inventory settings:', inventorySettings)}>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ إعدادات المخزون
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* الثيمات */}
          <TabsContent value="theme" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات المظهر</CardTitle>
                <CardDescription>
                  تخصيص مظهر النظام والألوان
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-base font-medium">وضع العرض</Label>
                    <p className="text-sm text-gray-600 mb-3">اختر بين الوضع الفاتح والداكن</p>
                    <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg text-center">
                      <div className="text-3xl mb-3">🎨</div>
                      <div className="text-lg font-medium text-blue-900 mb-2">الوضع الداكن والثيمات</div>
                      <div className="text-sm text-blue-700">ستكون متاحة في التحديث القادم</div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <Label className="text-base font-medium">ألوان الشركة</Label>
                    <p className="text-sm text-gray-600 mb-3">اختر اللون الأساسي للنظام</p>
                    <div className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg text-center">
                      <div className="text-3xl mb-3">🌈</div>
                      <div className="text-lg font-medium text-green-900 mb-2">الألوان المخصصة</div>
                      <div className="text-sm text-green-700">ستكون متاحة في التحديث القادم</div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <Label className="text-base font-medium">تبديل سريع</Label>
                    <p className="text-sm text-gray-600 mb-3">تبديل سريع بين الأوضاع</p>
                    <div className="p-6 bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200 rounded-lg text-center">
                      <div className="text-3xl mb-3">🔄</div>
                      <div className="text-lg font-medium text-purple-900 mb-2">التبديل السريع</div>
                      <div className="text-sm text-purple-700">ستكون متاحة في التحديث القادم</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* الإشعارات المتقدمة */}
          <TabsContent value="push" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>الإشعارات المتقدمة</CardTitle>
                <CardDescription>
                  إعدادات الإشعارات الفورية والتنبيهات الذكية
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-base font-medium">الإشعارات الفورية</Label>
                      <p className="text-sm text-gray-600">تلقي إشعارات فورية من النظام</p>
                    </div>
                    <div className="flex gap-2">
                      {!pushNotificationsEnabled ? (
                        <Button onClick={handleEnablePushNotifications}>
                          تفعيل الإشعارات
                        </Button>
                      ) : (
                        <Button variant="outline" onClick={testNotification}>
                          اختبار الإشعارات
                        </Button>
                      )}
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <Label className="text-base font-medium">أنواع الإشعارات</Label>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>تنبيهات المخزون المنخفض</Label>
                          <p className="text-sm text-gray-600">إشعار عند انخفاض المخزون</p>
                        </div>
                        <Switch defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label>إشعارات المبيعات الجديدة</Label>
                          <p className="text-sm text-gray-600">إشعار عند إتمام مبيعة جديدة</p>
                        </div>
                        <Switch defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label>تذكير النسخ الاحتياطية</Label>
                          <p className="text-sm text-gray-600">تذكير يومي لإنشاء نسخة احتياطية</p>
                        </div>
                        <Switch defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label>إشعارات التحديثات</Label>
                          <p className="text-sm text-gray-600">إشعار عند توفر تحديثات جديدة</p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* النسخ الاحتياطي */}
          <TabsContent value="backup" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إدارة النسخ الاحتياطي</CardTitle>
                <CardDescription>
                  إنشاء واستعادة النسخ الاحتياطية لحماية بياناتك
                </CardDescription>
              </CardHeader>
              <CardContent>
                <BackupManager />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
