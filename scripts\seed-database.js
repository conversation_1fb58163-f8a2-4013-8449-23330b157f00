const { drizzle } = require('drizzle-orm/better-sqlite3')
const Database = require('better-sqlite3')
const { join } = require('path')
const { mkdirSync } = require('fs')

// إنشاء مجلد البيانات إذا لم يكن موجوداً
try {
  mkdirSync(join(process.cwd(), 'data'), { recursive: true })
} catch (error) {
  // المجلد موجود بالفعل
}

const dbPath = join(process.cwd(), 'data', 'steel-trade.db')
const sqlite = new Database(dbPath)
const db = drizzle(sqlite)

// إنشاء الجداول
const createTables = () => {
  console.log('إنشاء الجداول...')

  // حذف الجداول الموجودة وإعادة إنشائها
  sqlite.exec('DROP TABLE IF EXISTS sale_items')
  sqlite.exec('DROP TABLE IF EXISTS sales')
  sqlite.exec('DROP TABLE IF EXISTS products')
  sqlite.exec('DROP TABLE IF EXISTS customers')
  sqlite.exec('DROP TABLE IF EXISTS suppliers')
  sqlite.exec('DROP TABLE IF EXISTS companies')

  // جدول الشركات
  sqlite.exec(`
    CREATE TABLE companies (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      email TEXT,
      phone TEXT,
      address TEXT,
      tax_number TEXT,
      logo TEXT,
      user_id TEXT NOT NULL,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL
    )
  `)

  // جدول العملاء
  sqlite.exec(`
    CREATE TABLE customers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      company TEXT,
      email TEXT,
      phone TEXT NOT NULL,
      address TEXT,
      tax_number TEXT,
      type TEXT DEFAULT 'individual' NOT NULL,
      status TEXT DEFAULT 'active' NOT NULL,
      credit_limit REAL DEFAULT 0 NOT NULL,
      current_debt REAL DEFAULT 0 NOT NULL,
      total_purchases REAL DEFAULT 0 NOT NULL,
      last_purchase INTEGER,
      notes TEXT,
      user_id TEXT NOT NULL,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL
    )
  `)

  // جدول الموردين
  sqlite.exec(`
    CREATE TABLE suppliers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      company TEXT,
      email TEXT,
      phone TEXT NOT NULL,
      address TEXT,
      tax_number TEXT,
      contact_person TEXT,
      payment_terms TEXT,
      status TEXT DEFAULT 'active' NOT NULL,
      total_purchases REAL DEFAULT 0 NOT NULL,
      last_purchase INTEGER,
      notes TEXT,
      user_id TEXT NOT NULL,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL
    )
  `)

  // جدول المنتجات
  sqlite.exec(`
    CREATE TABLE products (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      category TEXT NOT NULL,
      price REAL NOT NULL,
      cost REAL NOT NULL,
      unit TEXT NOT NULL,
      stock INTEGER DEFAULT 0 NOT NULL,
      min_stock INTEGER DEFAULT 0 NOT NULL,
      barcode TEXT,
      supplier TEXT,
      location TEXT,
      user_id TEXT NOT NULL,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL
    )
  `)

  // جدول المبيعات
  sqlite.exec(`
    CREATE TABLE sales (
      id TEXT PRIMARY KEY,
      customer_id TEXT,
      customer_name TEXT NOT NULL,
      subtotal REAL NOT NULL,
      discount REAL DEFAULT 0 NOT NULL,
      tax REAL DEFAULT 0 NOT NULL,
      total REAL NOT NULL,
      payment_type TEXT NOT NULL,
      paid_amount REAL DEFAULT 0 NOT NULL,
      remaining_amount REAL DEFAULT 0 NOT NULL,
      status TEXT DEFAULT 'completed' NOT NULL,
      notes TEXT,
      user_id TEXT NOT NULL,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL
    )
  `)

  // جدول عناصر المبيعات
  sqlite.exec(`
    CREATE TABLE sale_items (
      id TEXT PRIMARY KEY,
      sale_id TEXT NOT NULL,
      product_id TEXT NOT NULL,
      product_name TEXT NOT NULL,
      quantity INTEGER NOT NULL,
      unit_price REAL NOT NULL,
      total REAL NOT NULL,
      created_at INTEGER NOT NULL,
      FOREIGN KEY (sale_id) REFERENCES sales (id) ON DELETE CASCADE
    )
  `)

  console.log('تم إنشاء الجداول بنجاح!')
}

// إدراج بيانات تجريبية
const seedData = () => {
  console.log('إدراج البيانات التجريبية...')
  
  const now = Date.now()
  
  // بيانات المنتجات
  const products = [
    {
      id: 'prod_1',
      name: 'حديد تسليح 12 مم',
      description: 'حديد تسليح عالي الجودة قطر 12 مم',
      category: 'حديد تسليح',
      price: 850,
      cost: 750,
      unit: 'طن',
      stock: 50,
      min_stock: 10,
      barcode: '*********0123',
      supplier: 'شركة الحديد المتحدة',
      location: 'المخزن الرئيسي',
      user_id: 'user_1',
      created_at: now,
      updated_at: now
    },
    {
      id: 'prod_2',
      name: 'حديد تسليح 16 مم',
      description: 'حديد تسليح عالي الجودة قطر 16 مم',
      category: 'حديد تسليح',
      price: 850,
      cost: 750,
      unit: 'طن',
      stock: 30,
      min_stock: 5,
      barcode: '*********0124',
      supplier: 'شركة الحديد المتحدة',
      location: 'المخزن الرئيسي',
      user_id: 'user_1',
      created_at: now,
      updated_at: now
    },
    {
      id: 'prod_3',
      name: 'صاج مجلفن 2 مم',
      description: 'صاج مجلفن سماكة 2 مم',
      category: 'صاج',
      price: 1200,
      cost: 1000,
      unit: 'لوح',
      stock: 100,
      min_stock: 20,
      barcode: '*********0125',
      supplier: 'مصنع الصاج الحديث',
      location: 'المخزن الثانوي',
      user_id: 'user_1',
      created_at: now,
      updated_at: now
    }
  ]

  // بيانات العملاء
  const customers = [
    {
      id: 'cust_1',
      name: 'أحمد محمد علي',
      company: 'شركة البناء الحديث',
      email: '<EMAIL>',
      phone: '*********',
      address: 'صنعاء - شارع الزبيري',
      tax_number: '*********',
      type: 'company',
      status: 'active',
      credit_limit: 100000,
      current_debt: 0,
      total_purchases: 0,
      last_purchase: null,
      notes: 'عميل مميز',
      user_id: 'user_1',
      created_at: now,
      updated_at: now
    },
    {
      id: 'cust_2',
      name: 'فاطمة أحمد',
      company: null,
      email: '<EMAIL>',
      phone: '*********',
      address: 'تعز - شارع جمال',
      tax_number: null,
      type: 'individual',
      status: 'active',
      credit_limit: 50000,
      current_debt: 0,
      total_purchases: 0,
      last_purchase: null,
      notes: '',
      user_id: 'user_1',
      created_at: now,
      updated_at: now
    }
  ]

  // إدراج البيانات
  const insertProduct = sqlite.prepare(`
    INSERT INTO products (id, name, description, category, price, cost, unit, stock, min_stock, barcode, supplier, location, user_id, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `)

  const insertCustomer = sqlite.prepare(`
    INSERT INTO customers (id, name, company, email, phone, address, tax_number, type, status, credit_limit, current_debt, total_purchases, last_purchase, notes, user_id, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `)

  // إدراج المنتجات
  for (const product of products) {
    try {
      insertProduct.run(
        product.id, product.name, product.description, product.category,
        product.price, product.cost, product.unit, product.stock,
        product.min_stock, product.barcode, product.supplier, product.location,
        product.user_id, product.created_at, product.updated_at
      )
    } catch (error) {
      console.log(`المنتج ${product.name} موجود بالفعل`)
    }
  }

  // إدراج العملاء
  for (const customer of customers) {
    try {
      insertCustomer.run(
        customer.id, customer.name, customer.company, customer.email,
        customer.phone, customer.address, customer.tax_number, customer.type,
        customer.status, customer.credit_limit, customer.current_debt,
        customer.total_purchases, customer.last_purchase, customer.notes,
        customer.user_id, customer.created_at, customer.updated_at
      )
    } catch (error) {
      console.log(`العميل ${customer.name} موجود بالفعل`)
    }
  }

  console.log('تم إدراج البيانات التجريبية بنجاح!')
}

// تشغيل السكريبت
try {
  createTables()
  seedData()
  console.log('تم تهيئة قاعدة البيانات بنجاح!')
} catch (error) {
  console.error('خطأ في تهيئة قاعدة البيانات:', error)
} finally {
  sqlite.close()
}
