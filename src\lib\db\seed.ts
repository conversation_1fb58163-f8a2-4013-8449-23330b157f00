import { db } from './index'
import { companies, customers, products, suppliers } from './sqlite-schema'

export async function seedDatabase() {
  try {
    console.log('🌱 بدء إضافة البيانات الأولية...')

    // إضافة شركة افتراضية
    const defaultCompany = {
      id: 'default-company',
      name: 'تجارة الحديد والمعدات',
      email: '<EMAIL>',
      phone: '+967-1-234567',
      address: 'صنعاء، اليمن',
      taxNumber: '*********',
      logo: null,
      userId: 'default-user',
      createdAt: Date.now(),
      updatedAt: Date.now(),
    }

    await db.insert(companies).values(defaultCompany).onConflictDoNothing()
    console.log('✅ تم إضافة الشركة الافتراضية')

    // إضافة عملاء تجريبيين
    const sampleCustomers = [
      {
        id: 'customer-1',
        name: 'أحمد محمد علي',
        company: 'شركة البناء الحديث',
        email: '<EMAIL>',
        phone: '+967-**********',
        address: 'شارع الزبيري، صنعاء',
        taxNumber: '*********',
        type: 'company' as const,
        status: 'active' as const,
        creditLimit: 500000,
        currentDebt: 0,
        totalPurchases: 0,
        lastPurchase: null,
        companyId: 'default-company',
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'customer-2',
        name: 'فاطمة سالم',
        company: null,
        email: '<EMAIL>',
        phone: '+967-**********',
        address: 'حي السبعين، صنعاء',
        taxNumber: null,
        type: 'individual' as const,
        status: 'active' as const,
        creditLimit: 100000,
        currentDebt: 0,
        totalPurchases: 0,
        lastPurchase: null,
        companyId: 'default-company',
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'customer-3',
        name: 'محمد عبدالله',
        company: 'مؤسسة الإعمار للمقاولات',
        email: '<EMAIL>',
        phone: '+967-**********',
        address: 'شارع الستين، صنعاء',
        taxNumber: '*********',
        type: 'company' as const,
        status: 'active' as const,
        creditLimit: 1000000,
        currentDebt: 0,
        totalPurchases: 0,
        lastPurchase: null,
        companyId: 'default-company',
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }
    ]

    await db.insert(customers).values(sampleCustomers).onConflictDoNothing()
    console.log('✅ تم إضافة العملاء التجريبيين')

    // إضافة منتجات تجريبية
    const sampleProducts = [
      {
        id: 'product-1',
        name: 'حديد تسليح 12 مم',
        description: 'حديد تسليح عالي الجودة قطر 12 مم',
        price: 850000,
        cost: 750000,
        unit: 'طن',
        sku: 'STEEL-12MM',
        barcode: '*********0123',
        category: 'حديد التسليح',
        stock: 50,
        minStock: 10,
        supplier: 'مصنع الحديد الوطني',
        location: 'المخزن الرئيسي - رف A1',
        image: null,
        companyId: 'default-company',
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'product-2',
        name: 'حديد تسليح 16 مم',
        description: 'حديد تسليح عالي الجودة قطر 16 مم',
        price: 860000,
        cost: 760000,
        unit: 'طن',
        sku: 'STEEL-16MM',
        barcode: '*********0124',
        category: 'حديد التسليح',
        stock: 30,
        minStock: 5,
        supplier: 'مصنع الحديد الوطني',
        location: 'المخزن الرئيسي - رف A2',
        image: null,
        companyId: 'default-company',
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'product-3',
        name: 'صاج مجلفن 2 مم',
        description: 'صاج مجلفن سماكة 2 مم مقاس 1.2 × 2.4 متر',
        price: 45000,
        cost: 38000,
        unit: 'لوح',
        sku: 'SHEET-2MM',
        barcode: '*********0125',
        category: 'الصاج والألواح',
        stock: 100,
        minStock: 20,
        supplier: 'مصنع الصاج المتطور',
        location: 'المخزن الثانوي - رف B1',
        image: null,
        companyId: 'default-company',
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'product-4',
        name: 'أنبوب حديد 2 بوصة',
        description: 'أنبوب حديد أسود قطر 2 بوصة',
        price: 25000,
        cost: 20000,
        unit: 'متر',
        sku: 'PIPE-2INCH',
        barcode: '*********0126',
        category: 'الأنابيب',
        stock: 200,
        minStock: 50,
        supplier: 'مصنع الأنابيب الحديثة',
        location: 'المخزن الخارجي - منطقة C',
        image: null,
        companyId: 'default-company',
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'product-5',
        name: 'مسامير 10 سم',
        description: 'مسامير حديد طول 10 سم',
        price: 15000,
        cost: 12000,
        unit: 'كيلو',
        sku: 'NAILS-10CM',
        barcode: '*********0127',
        category: 'المسامير والبراغي',
        stock: 500,
        minStock: 100,
        supplier: 'مصنع المسامير الذهبية',
        location: 'المخزن الرئيسي - رف D1',
        image: null,
        companyId: 'default-company',
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }
    ]

    await db.insert(products).values(sampleProducts).onConflictDoNothing()
    console.log('✅ تم إضافة المنتجات التجريبية')

    // إضافة موردين تجريبيين
    const sampleSuppliers = [
      {
        id: 'supplier-1',
        name: 'مصنع الحديد الوطني',
        company: 'شركة الحديد الوطنية المحدودة',
        email: '<EMAIL>',
        phone: '+967-1-345678',
        address: 'المنطقة الصناعية، صنعاء',
        taxNumber: '*********',
        status: 'active' as const,
        totalPurchases: 0,
        lastPurchase: null,
        companyId: 'default-company',
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'supplier-2',
        name: 'مصنع الصاج المتطور',
        company: 'مؤسسة الصاج المتطور',
        email: '<EMAIL>',
        phone: '+967-1-456789',
        address: 'شارع الصناعة، صنعاء',
        taxNumber: '*********',
        status: 'active' as const,
        totalPurchases: 0,
        lastPurchase: null,
        companyId: 'default-company',
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 'supplier-3',
        name: 'مصنع الأنابيب الحديثة',
        company: 'شركة الأنابيب الحديثة',
        email: '<EMAIL>',
        phone: '+967-1-567890',
        address: 'المنطقة الحرة، عدن',
        taxNumber: '*********',
        status: 'active' as const,
        totalPurchases: 0,
        lastPurchase: null,
        companyId: 'default-company',
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }
    ]

    await db.insert(suppliers).values(sampleSuppliers).onConflictDoNothing()
    console.log('✅ تم إضافة الموردين التجريبيين')

    console.log('🎉 تم إكمال إضافة البيانات الأولية بنجاح!')
    
  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات الأولية:', error)
    throw error
  }
}
