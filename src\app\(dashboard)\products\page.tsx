'use client'

import { useState, useEffect } from 'react'
import { realDataManager, Product } from '@/lib/real-storage'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  ArrowLeft, Search, Plus, Package, AlertTriangle, 
  TrendingUp, Building, Edit, X, Save, Trash2
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

export default function ProductsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('الكل')
  const [selectedStatus, setSelectedStatus] = useState('الكل')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [products, setProducts] = useState<Product[]>([])
  const [newProduct, setNewProduct] = useState({
    name: '',
    description: '',
    category: '',
    price: 0,
    cost: 0,
    stock: 0,
    minStock: 0,
    barcode: '',
    unit: '',
    supplier: '',
    location: ''
  })

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    realDataManager.initializeData()
    loadData()
  }, [])

  const loadData = () => {
    setProducts(realDataManager.getProducts())
  }

  // تصفية المنتجات
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.barcode?.includes(searchTerm)
    const matchesCategory = selectedCategory === 'الكل' || product.category === selectedCategory
    const matchesStatus = selectedStatus === 'الكل' || getStockStatus(product) === selectedStatus
    return matchesSearch && matchesCategory && matchesStatus
  })

  // الفئات المتاحة
  const categories = ['الكل', ...Array.from(new Set(products.map(p => p.category)))]
  const statuses = ['الكل', 'متوفر', 'مخزون منخفض', 'نفد المخزون']

  // حالة المخزون
  const getStockStatus = (product: Product) => {
    if (product.stock === 0) return 'نفد المخزون'
    if (product.stock <= product.minStock) return 'مخزون منخفض'
    return 'متوفر'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'متوفر': return 'bg-green-100 text-green-800'
      case 'مخزون منخفض': return 'bg-yellow-100 text-yellow-800'
      case 'نفد المخزون': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // إحصائيات المنتجات
  const stats = {
    totalProducts: products.length,
    availableProducts: products.filter(p => p.stock > p.minStock).length,
    lowStockProducts: products.filter(p => p.stock > 0 && p.stock <= p.minStock).length,
    outOfStockProducts: products.filter(p => p.stock === 0).length,
    totalValue: products.reduce((sum, p) => sum + (p.stock * p.cost), 0)
  }

  // إضافة منتج جديد
  const addNewProduct = () => {
    if (!newProduct.name || !newProduct.category) return

    const product = realDataManager.addProduct(newProduct)
    loadData()
    setNewProduct({
      name: '',
      description: '',
      category: '',
      price: 0,
      cost: 0,
      stock: 0,
      minStock: 0,
      barcode: '',
      unit: '',
      supplier: '',
      location: ''
    })
    setShowAddModal(false)
  }

  // تحديث منتج
  const updateProduct = () => {
    if (!selectedProduct) return

    realDataManager.updateProduct(selectedProduct.id, {
      name: selectedProduct.name,
      description: selectedProduct.description,
      category: selectedProduct.category,
      price: selectedProduct.price,
      cost: selectedProduct.cost,
      stock: selectedProduct.stock,
      minStock: selectedProduct.minStock,
      barcode: selectedProduct.barcode,
      unit: selectedProduct.unit,
      supplier: selectedProduct.supplier,
      location: selectedProduct.location
    })

    loadData()
    setShowEditModal(false)
    setSelectedProduct(null)
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
                <ArrowLeft className="h-4 w-4 ml-2" />
                العودة
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">إدارة المنتجات</h1>
                <p className="text-sm text-gray-600">إدارة مخزون الحديد والمعدات</p>
              </div>
            </div>
            
            <Button onClick={() => setShowAddModal(true)} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              إضافة منتج جديد
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* إحصائيات المنتجات */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">إجمالي المنتجات</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">متوفر</p>
                  <p className="text-2xl font-bold text-green-600">{stats.availableProducts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <AlertTriangle className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">مخزون منخفض</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.lowStockProducts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <X className="h-6 w-6 text-red-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">نفد المخزون</p>
                  <p className="text-2xl font-bold text-red-600">{stats.outOfStockProducts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Building className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">قيمة المخزون</p>
                  <p className="text-lg font-bold text-purple-600">{formatCurrency(stats.totalValue)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* البحث والفلاتر */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في المنتجات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                {categories.map(category => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </Button>
                ))}
              </div>

              <div className="flex gap-2 flex-wrap">
                {statuses.map(status => (
                  <Button
                    key={status}
                    variant={selectedStatus === status ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus(status)}
                  >
                    {status}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول المنتجات */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المنتجات ({filteredProducts.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-right py-3 px-4 font-medium text-gray-900">المنتج</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">الفئة</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">السعر</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">المخزون</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">الحالة</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProducts.map((product) => (
                    <tr key={product.id} className="border-b hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{product.name}</div>
                          <div className="text-sm text-gray-600">{product.description}</div>
                          {product.barcode && (
                            <div className="text-xs text-gray-500">الباركود: {product.barcode}</div>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <Badge className="bg-blue-100 text-blue-800">
                          {product.category}
                        </Badge>
                      </td>
                      <td className="py-4 px-4">
                        <div className="font-medium text-green-600">
                          {formatCurrency(product.price)}
                        </div>
                        <div className="text-sm text-gray-600">
                          التكلفة: {formatCurrency(product.cost)}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="font-medium">
                          {product.stock} {product.unit}
                        </div>
                        <div className="text-sm text-gray-600">
                          الحد الأدنى: {product.minStock} {product.unit}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <Badge className={getStatusColor(getStockStatus(product))}>
                          {getStockStatus(product)}
                        </Badge>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedProduct(product)
                              setShowEditModal(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {filteredProducts.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد منتجات مطابقة للبحث
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* مودال إضافة منتج */}
      {showAddModal && (
        <div className="fixed inset-0 z-50">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowAddModal(false)} />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">إضافة منتج جديد</h2>
                <Button variant="ghost" size="sm" onClick={() => setShowAddModal(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">اسم المنتج *</label>
                  <Input
                    placeholder="أدخل اسم المنتج"
                    value={newProduct.name}
                    onChange={(e) => setNewProduct({...newProduct, name: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الفئة *</label>
                  <Input
                    placeholder="أدخل فئة المنتج"
                    value={newProduct.category}
                    onChange={(e) => setNewProduct({...newProduct, category: e.target.value})}
                  />
                </div>

                <div className="col-span-2">
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الوصف</label>
                  <Input
                    placeholder="أدخل وصف المنتج"
                    value={newProduct.description}
                    onChange={(e) => setNewProduct({...newProduct, description: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">سعر البيع</label>
                  <Input
                    type="number"
                    placeholder="سعر البيع بالريال اليمني"
                    value={newProduct.price}
                    onChange={(e) => setNewProduct({...newProduct, price: parseInt(e.target.value) || 0})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">سعر التكلفة</label>
                  <Input
                    type="number"
                    placeholder="سعر التكلفة بالريال اليمني"
                    value={newProduct.cost}
                    onChange={(e) => setNewProduct({...newProduct, cost: parseInt(e.target.value) || 0})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الكمية الحالية</label>
                  <Input
                    type="number"
                    placeholder="الكمية المتوفرة"
                    value={newProduct.stock}
                    onChange={(e) => setNewProduct({...newProduct, stock: parseInt(e.target.value) || 0})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الحد الأدنى للمخزون</label>
                  <Input
                    type="number"
                    placeholder="الحد الأدنى للتنبيه"
                    value={newProduct.minStock}
                    onChange={(e) => setNewProduct({...newProduct, minStock: parseInt(e.target.value) || 0})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الوحدة</label>
                  <Input
                    placeholder="مثل: كيلو، طن، قطعة"
                    value={newProduct.unit}
                    onChange={(e) => setNewProduct({...newProduct, unit: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الباركود</label>
                  <Input
                    placeholder="رقم الباركود (اختياري)"
                    value={newProduct.barcode}
                    onChange={(e) => setNewProduct({...newProduct, barcode: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">المورد</label>
                  <Input
                    placeholder="اسم المورد"
                    value={newProduct.supplier}
                    onChange={(e) => setNewProduct({...newProduct, supplier: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الموقع</label>
                  <Input
                    placeholder="موقع التخزين"
                    value={newProduct.location}
                    onChange={(e) => setNewProduct({...newProduct, location: e.target.value})}
                  />
                </div>
              </div>

              <div className="flex gap-3 pt-6">
                <Button
                  className="flex-1"
                  onClick={addNewProduct}
                  disabled={!newProduct.name || !newProduct.category}
                >
                  <Save className="h-4 w-4 ml-2" />
                  حفظ المنتج
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowAddModal(false)}
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* مودال تعديل منتج */}
      {showEditModal && selectedProduct && (
        <div className="fixed inset-0 z-50">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowEditModal(false)} />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">تعديل بيانات المنتج</h2>
                <Button variant="ghost" size="sm" onClick={() => setShowEditModal(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">اسم المنتج *</label>
                  <Input
                    placeholder="أدخل اسم المنتج"
                    value={selectedProduct.name}
                    onChange={(e) => setSelectedProduct({...selectedProduct, name: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الفئة *</label>
                  <Input
                    placeholder="أدخل فئة المنتج"
                    value={selectedProduct.category}
                    onChange={(e) => setSelectedProduct({...selectedProduct, category: e.target.value})}
                  />
                </div>

                <div className="col-span-2">
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الوصف</label>
                  <Input
                    placeholder="أدخل وصف المنتج"
                    value={selectedProduct.description}
                    onChange={(e) => setSelectedProduct({...selectedProduct, description: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">سعر البيع</label>
                  <Input
                    type="number"
                    placeholder="سعر البيع بالريال اليمني"
                    value={selectedProduct.price}
                    onChange={(e) => setSelectedProduct({...selectedProduct, price: parseInt(e.target.value) || 0})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">سعر التكلفة</label>
                  <Input
                    type="number"
                    placeholder="سعر التكلفة بالريال اليمني"
                    value={selectedProduct.cost}
                    onChange={(e) => setSelectedProduct({...selectedProduct, cost: parseInt(e.target.value) || 0})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الكمية الحالية</label>
                  <Input
                    type="number"
                    placeholder="الكمية المتوفرة"
                    value={selectedProduct.stock}
                    onChange={(e) => setSelectedProduct({...selectedProduct, stock: parseInt(e.target.value) || 0})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الحد الأدنى للمخزون</label>
                  <Input
                    type="number"
                    placeholder="الحد الأدنى للتنبيه"
                    value={selectedProduct.minStock}
                    onChange={(e) => setSelectedProduct({...selectedProduct, minStock: parseInt(e.target.value) || 0})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الوحدة</label>
                  <Input
                    placeholder="مثل: كيلو، طن، قطعة"
                    value={selectedProduct.unit}
                    onChange={(e) => setSelectedProduct({...selectedProduct, unit: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الباركود</label>
                  <Input
                    placeholder="رقم الباركود (اختياري)"
                    value={selectedProduct.barcode || ''}
                    onChange={(e) => setSelectedProduct({...selectedProduct, barcode: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">المورد</label>
                  <Input
                    placeholder="اسم المورد"
                    value={selectedProduct.supplier}
                    onChange={(e) => setSelectedProduct({...selectedProduct, supplier: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">الموقع</label>
                  <Input
                    placeholder="موقع التخزين"
                    value={selectedProduct.location}
                    onChange={(e) => setSelectedProduct({...selectedProduct, location: e.target.value})}
                  />
                </div>
              </div>

              <div className="flex gap-3 pt-6">
                <Button
                  className="flex-1"
                  onClick={updateProduct}
                  disabled={!selectedProduct.name || !selectedProduct.category}
                >
                  <Save className="h-4 w-4 ml-2" />
                  حفظ التغييرات
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowEditModal(false)}
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
