'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { LogIn, User } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function FallbackSignInPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // محاكاة تسجيل الدخول
    setTimeout(() => {
      const mockUser = {
        id: 'user_1',
        firstName: 'مستخدم',
        lastName: 'تجريبي',
        email: email || '<EMAIL>'
      }
      
      localStorage.setItem('fallback-user', JSON.stringify(mockUser))
      setIsLoading(false)
      router.push('/dashboard')
    }, 1000)
  }

  const handleDemoLogin = () => {
    setEmail('<EMAIL>')
    setPassword('demo123')
    
    const mockUser = {
      id: 'user_demo',
      firstName: 'مستخدم',
      lastName: 'تجريبي',
      email: '<EMAIL>'
    }
    
    localStorage.setItem('fallback-user', JSON.stringify(mockUser))
    router.push('/dashboard')
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50" dir="rtl">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            تسجيل الدخول
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            إلى نظام المحاسبة الحديث
          </p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LogIn className="h-5 w-5" />
              تسجيل الدخول
            </CardTitle>
            <CardDescription>
              أدخل بياناتك للوصول إلى النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">كلمة المرور</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
              </Button>
            </form>
            
            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">أو</span>
                </div>
              </div>
              
              <Button
                variant="outline"
                className="w-full mt-4 flex items-center gap-2"
                onClick={handleDemoLogin}
              >
                <User className="h-4 w-4" />
                دخول تجريبي سريع
              </Button>
            </div>
            
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                هذا نظام تجريبي للعرض فقط
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
