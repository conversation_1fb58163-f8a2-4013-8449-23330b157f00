import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "../init";

const expenseSchema = z.object({
  id: z.string(),
  description: z.string(),
  amount: z.number(),
  date: z.string(),
  category: z.enum(['rent', 'utilities', 'office_supplies', 'maintenance', 'software', 'transportation', 'marketing', 'other']),
  vendor: z.string(),
  paymentMethod: z.enum(['bank_transfer', 'credit_card', 'cash', 'check']),
  status: z.enum(['paid', 'pending', 'overdue']),
  receiptNumber: z.string(),
  notes: z.string().optional(),
});

const createExpenseSchema = z.object({
  description: z.string().min(1),
  amount: z.number().positive(),
  category: z.enum(['rent', 'utilities', 'office_supplies', 'maintenance', 'software', 'transportation', 'marketing', 'other']),
  vendor: z.string().min(1),
  paymentMethod: z.enum(['bank_transfer', 'credit_card', 'cash', 'check']),
  receiptNumber: z.string(),
  notes: z.string().optional(),
});

const updateExpenseSchema = z.object({
  id: z.string(),
  description: z.string().optional(),
  amount: z.number().positive().optional(),
  category: z.enum(['rent', 'utilities', 'office_supplies', 'maintenance', 'software', 'transportation', 'marketing', 'other']).optional(),
  vendor: z.string().optional(),
  paymentMethod: z.enum(['bank_transfer', 'credit_card', 'cash', 'check']).optional(),
  status: z.enum(['paid', 'pending', 'overdue']).optional(),
  receiptNumber: z.string().optional(),
  notes: z.string().optional(),
});

// Mock data - في التطبيق الحقيقي، هذه البيانات ستأتي من قاعدة البيانات
const mockExpenses = [
  {
    id: 'EXP-001',
    description: 'إيجار المكتب - يناير 2024',
    amount: 8000,
    date: '2024-01-01',
    category: 'rent' as const,
    vendor: 'شركة العقارات المتميزة',
    paymentMethod: 'bank_transfer' as const,
    status: 'paid' as const,
    receiptNumber: 'REC-001',
    notes: 'إيجار شهري'
  },
  {
    id: 'EXP-002',
    description: 'فواتير الكهرباء والماء',
    amount: 1200,
    date: '2024-01-05',
    category: 'utilities' as const,
    vendor: 'شركة الكهرباء والماء',
    paymentMethod: 'credit_card' as const,
    status: 'paid' as const,
    receiptNumber: 'REC-002',
    notes: ''
  },
  {
    id: 'EXP-003',
    description: 'مستلزمات مكتبية',
    amount: 450,
    date: '2024-01-08',
    category: 'office_supplies' as const,
    vendor: 'مكتبة الأعمال',
    paymentMethod: 'cash' as const,
    status: 'paid' as const,
    receiptNumber: 'REC-003',
    notes: 'أوراق وأقلام ومستلزمات'
  },
  {
    id: 'EXP-004',
    description: 'صيانة أجهزة الكمبيوتر',
    amount: 2500,
    date: '2024-01-10',
    category: 'maintenance' as const,
    vendor: 'مركز الصيانة التقني',
    paymentMethod: 'bank_transfer' as const,
    status: 'pending' as const,
    receiptNumber: 'REC-004',
    notes: 'صيانة دورية للأجهزة'
  }
];

export const expensesRouter = createTRPCRouter({
  // الحصول على جميع المصروفات
  getAll: publicProcedure.query(async () => {
    // في التطبيق الحقيقي، سيتم جلب البيانات من قاعدة البيانات
    return mockExpenses;
  }),

  // الحصول على مصروف واحد بالمعرف
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const expense = mockExpenses.find(e => e.id === input.id);
      if (!expense) {
        throw new Error('Expense not found');
      }
      return expense;
    }),

  // إنشاء مصروف جديد
  create: publicProcedure
    .input(createExpenseSchema)
    .mutation(async ({ input }) => {
      // في التطبيق الحقيقي، سيتم حفظ البيانات في قاعدة البيانات
      const newExpense = {
        id: `EXP-${String(mockExpenses.length + 1).padStart(3, '0')}`,
        date: new Date().toISOString().split('T')[0],
        status: 'pending' as const,
        ...input,
      };
      
      mockExpenses.push(newExpense);
      return newExpense;
    }),

  // تحديث مصروف
  update: publicProcedure
    .input(updateExpenseSchema)
    .mutation(async ({ input }) => {
      const expenseIndex = mockExpenses.findIndex(e => e.id === input.id);
      if (expenseIndex === -1) {
        throw new Error('Expense not found');
      }

      const updatedExpense = {
        ...mockExpenses[expenseIndex],
        ...input,
      };

      mockExpenses[expenseIndex] = updatedExpense;
      return updatedExpense;
    }),

  // حذف مصروف
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      const expenseIndex = mockExpenses.findIndex(e => e.id === input.id);
      if (expenseIndex === -1) {
        throw new Error('Expense not found');
      }

      const deletedExpense = mockExpenses.splice(expenseIndex, 1)[0];
      return deletedExpense;
    }),

  // الحصول على إحصائيات المصروفات
  getStats: publicProcedure.query(async () => {
    const totalExpenses = mockExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const paidExpenses = mockExpenses
      .filter(e => e.status === 'paid')
      .reduce((sum, expense) => sum + expense.amount, 0);
    const pendingExpenses = mockExpenses
      .filter(e => e.status === 'pending')
      .reduce((sum, expense) => sum + expense.amount, 0);
    const overdueExpenses = mockExpenses
      .filter(e => e.status === 'overdue')
      .reduce((sum, expense) => sum + expense.amount, 0);

    // تجميع المصروفات حسب الفئة
    const categoryBreakdown = mockExpenses.reduce((acc, expense) => {
      if (!acc[expense.category]) {
        acc[expense.category] = { total: 0, count: 0 };
      }
      acc[expense.category].total += expense.amount;
      acc[expense.category].count += 1;
      return acc;
    }, {} as Record<string, { total: number; count: number }>);

    return {
      total: totalExpenses,
      paid: paidExpenses,
      pending: pendingExpenses,
      overdue: overdueExpenses,
      count: mockExpenses.length,
      paidCount: mockExpenses.filter(e => e.status === 'paid').length,
      pendingCount: mockExpenses.filter(e => e.status === 'pending').length,
      overdueCount: mockExpenses.filter(e => e.status === 'overdue').length,
      categoryBreakdown,
    };
  }),

  // البحث في المصروفات
  search: publicProcedure
    .input(z.object({
      query: z.string().optional(),
      category: z.enum(['rent', 'utilities', 'office_supplies', 'maintenance', 'software', 'transportation', 'marketing', 'other']).optional(),
      status: z.enum(['paid', 'pending', 'overdue']).optional(),
      dateFrom: z.string().optional(),
      dateTo: z.string().optional(),
    }))
    .query(async ({ input }) => {
      let filteredExpenses = mockExpenses;

      if (input.query) {
        const query = input.query.toLowerCase();
        filteredExpenses = filteredExpenses.filter(expense =>
          expense.description.toLowerCase().includes(query) ||
          expense.vendor.toLowerCase().includes(query) ||
          expense.id.toLowerCase().includes(query) ||
          expense.receiptNumber.toLowerCase().includes(query)
        );
      }

      if (input.category) {
        filteredExpenses = filteredExpenses.filter(expense => expense.category === input.category);
      }

      if (input.status) {
        filteredExpenses = filteredExpenses.filter(expense => expense.status === input.status);
      }

      if (input.dateFrom) {
        filteredExpenses = filteredExpenses.filter(expense => expense.date >= input.dateFrom!);
      }

      if (input.dateTo) {
        filteredExpenses = filteredExpenses.filter(expense => expense.date <= input.dateTo!);
      }

      return filteredExpenses;
    }),

  // الحصول على المصروفات حسب الفئة
  getByCategory: publicProcedure
    .input(z.object({
      category: z.enum(['rent', 'utilities', 'office_supplies', 'maintenance', 'software', 'transportation', 'marketing', 'other'])
    }))
    .query(async ({ input }) => {
      return mockExpenses.filter(expense => expense.category === input.category);
    }),
});

export type ExpensesRouter = typeof expensesRouter;
