import { z } from 'zod'
import { createTRPCRouter, publicProcedure } from '../init'

// Mock data for customers
const mockCustomers = [
  {
    id: '1',
    name: 'أحمد محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    address: 'الرياض، المملكة العربية السعودية',
    taxNumber: '*********',
    companyId: '1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    name: 'فاطمة علي',
    email: '<EMAIL>',
    phone: '+966507654321',
    address: 'جدة، المملكة العربية السعودية',
    taxNumber: '*********',
    companyId: '1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '3',
    name: 'محمد السعيد',
    email: '<EMAIL>',
    phone: '+966509876543',
    address: 'الدمام، المملكة العربية السعودية',
    taxNumber: '*********',
    companyId: '1',
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

const createCustomerSchema = z.object({
  name: z.string().min(1, 'اسم العميل مطلوب'),
  email: z.string().email('البريد الإلكتروني غير صحيح').optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  taxNumber: z.string().optional(),
  companyId: z.string().min(1, 'معرف الشركة مطلوب'),
})

const updateCustomerSchema = createCustomerSchema.partial().extend({
  id: z.string(),
})

export const customersRouter = createTRPCRouter({
  // الحصول على جميع العملاء
  getAll: publicProcedure.query(async () => {
    return mockCustomers;
  }),

  // الحصول على عميل واحد
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const customer = mockCustomers.find(c => c.id === input.id);
      if (!customer) {
        throw new Error('Customer not found');
      }
      return customer;
    }),

  // إنشاء عميل جديد
  create: publicProcedure
    .input(createCustomerSchema)
    .mutation(async ({ input }) => {
      const newCustomer = {
        id: String(mockCustomers.length + 1),
        ...input,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      mockCustomers.push(newCustomer);
      return newCustomer;
    }),

  // تحديث عميل
  update: publicProcedure
    .input(updateCustomerSchema)
    .mutation(async ({ input }) => {
      const { id, ...updateData } = input;
      const customerIndex = mockCustomers.findIndex(c => c.id === id);
      
      if (customerIndex === -1) {
        throw new Error('Customer not found');
      }
      
      mockCustomers[customerIndex] = {
        ...mockCustomers[customerIndex],
        ...updateData,
        updatedAt: new Date(),
      };
      
      return mockCustomers[customerIndex];
    }),

  // حذف عميل
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      const customerIndex = mockCustomers.findIndex(c => c.id === input.id);
      
      if (customerIndex === -1) {
        throw new Error('Customer not found');
      }
      
      mockCustomers.splice(customerIndex, 1);
      return { success: true };
    }),

  // البحث في العملاء
  search: publicProcedure
    .input(z.object({ query: z.string() }))
    .query(async ({ input }) => {
      const query = input.query.toLowerCase();
      return mockCustomers.filter(customer =>
        customer.name.toLowerCase().includes(query) ||
        customer.email?.toLowerCase().includes(query) ||
        customer.phone?.includes(query)
      );
    }),
});
