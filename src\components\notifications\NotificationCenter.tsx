'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Bell, AlertTriangle, Package, DollarSign, Users, X, Check } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { realDataManager } from '@/lib/real-storage'

interface Notification {
  id: string
  type: 'warning' | 'info' | 'success' | 'error'
  title: string
  message: string
  timestamp: number
  read: boolean
  actionUrl?: string
}

export default function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [showAll, setShowAll] = useState(false)

  useEffect(() => {
    generateNotifications()
    // تحديث الإشعارات كل 5 دقائق
    const interval = setInterval(generateNotifications, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  const generateNotifications = () => {
    const products = realDataManager.getProducts()
    const sales = realDataManager.getSales()
    const customers = realDataManager.getCustomers()
    
    const newNotifications: Notification[] = []

    // إشعارات المخزون المنخفض
    const lowStockProducts = products.filter(p => p.stock <= p.minStock && p.stock > 0)
    if (lowStockProducts.length > 0) {
      newNotifications.push({
        id: 'low-stock-' + Date.now(),
        type: 'warning',
        title: 'تنبيه مخزون منخفض',
        message: `${lowStockProducts.length} منتج لديه مخزون منخفض`,
        timestamp: Date.now(),
        read: false,
        actionUrl: '/inventory'
      })
    }

    // إشعارات نفاد المخزون
    const outOfStockProducts = products.filter(p => p.stock === 0)
    if (outOfStockProducts.length > 0) {
      newNotifications.push({
        id: 'out-of-stock-' + Date.now(),
        type: 'error',
        title: 'نفاد المخزون',
        message: `${outOfStockProducts.length} منتج نفد من المخزون`,
        timestamp: Date.now(),
        read: false,
        actionUrl: '/inventory'
      })
    }

    // إشعارات المبيعات اليومية
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const todaySales = sales.filter(sale => new Date(sale.createdAt) >= startOfDay)
    const todayRevenue = todaySales.reduce((sum, sale) => sum + sale.total, 0)
    
    if (todaySales.length > 0) {
      newNotifications.push({
        id: 'daily-sales-' + Date.now(),
        type: 'success',
        title: 'مبيعات اليوم',
        message: `تم تحقيق ${formatCurrency(todayRevenue)} من ${todaySales.length} عملية بيع`,
        timestamp: Date.now(),
        read: false,
        actionUrl: '/reports'
      })
    }

    // إشعارات العملاء الجدد
    const recentCustomers = customers.filter(c => {
      const customerDate = new Date(c.createdAt)
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      return customerDate >= weekAgo
    })
    
    if (recentCustomers.length > 0) {
      newNotifications.push({
        id: 'new-customers-' + Date.now(),
        type: 'info',
        title: 'عملاء جدد',
        message: `انضم ${recentCustomers.length} عميل جديد هذا الأسبوع`,
        timestamp: Date.now(),
        read: false,
        actionUrl: '/customers'
      })
    }

    // إشعارات الفواتير المتأخرة (محاكاة)
    const overdueInvoices = Math.floor(Math.random() * 3) // محاكاة فواتير متأخرة
    if (overdueInvoices > 0) {
      newNotifications.push({
        id: 'overdue-invoices-' + Date.now(),
        type: 'warning',
        title: 'فواتير متأخرة',
        message: `لديك ${overdueInvoices} فاتورة متأخرة السداد`,
        timestamp: Date.now(),
        read: false,
        actionUrl: '/invoices'
      })
    }

    setNotifications(prev => {
      // دمج الإشعارات الجديدة مع القديمة وإزالة المكررات
      const existingIds = prev.map(n => n.id)
      const uniqueNew = newNotifications.filter(n => !existingIds.includes(n.id))
      return [...uniqueNew, ...prev].slice(0, 20) // الاحتفاظ بآخر 20 إشعار
    })
  }

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })))
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const getIcon = (type: string) => {
    switch (type) {
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'success': return <DollarSign className="h-5 w-5 text-green-500" />
      case 'info': return <Users className="h-5 w-5 text-blue-500" />
      default: return <Bell className="h-5 w-5 text-gray-500" />
    }
  }

  const getBadgeColor = (type: string) => {
    switch (type) {
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      case 'error': return 'bg-red-100 text-red-800'
      case 'success': return 'bg-green-100 text-green-800'
      case 'info': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const unreadCount = notifications.filter(n => !n.read).length
  const displayNotifications = showAll ? notifications : notifications.slice(0, 5)

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            الإشعارات
            {unreadCount > 0 && (
              <Badge className="bg-red-500 text-white">
                {unreadCount}
              </Badge>
            )}
          </CardTitle>
          {unreadCount > 0 && (
            <Button variant="ghost" size="sm" onClick={markAllAsRead}>
              <Check className="h-4 w-4 ml-1" />
              قراءة الكل
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {displayNotifications.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Bell className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p>لا توجد إشعارات جديدة</p>
          </div>
        ) : (
          <>
            {displayNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-3 rounded-lg border transition-all ${
                  notification.read 
                    ? 'bg-gray-50 border-gray-200' 
                    : 'bg-white border-blue-200 shadow-sm'
                }`}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {getIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className={`text-sm font-medium ${
                        notification.read ? 'text-gray-600' : 'text-gray-900'
                      }`}>
                        {notification.title}
                      </h4>
                      <div className="flex items-center gap-1">
                        <Badge className={getBadgeColor(notification.type)}>
                          {notification.type === 'warning' ? 'تحذير' :
                           notification.type === 'error' ? 'خطأ' :
                           notification.type === 'success' ? 'نجح' : 'معلومات'}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeNotification(notification.id)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <p className={`text-sm ${
                      notification.read ? 'text-gray-500' : 'text-gray-700'
                    }`}>
                      {notification.message}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-400">
                        {new Date(notification.timestamp).toLocaleTimeString('ar-SA')}
                      </span>
                      {!notification.read && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => markAsRead(notification.id)}
                          className="text-xs"
                        >
                          تم القراءة
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {notifications.length > 5 && (
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setShowAll(!showAll)}
              >
                {showAll ? 'إظهار أقل' : `إظهار جميع الإشعارات (${notifications.length})`}
              </Button>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
