import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button Component', () => {
  test('renders button with text', () => {
    render(<Button>اختبار الزر</Button>)
    expect(screen.getByRole('button', { name: 'اختبار الزر' })).toBeInTheDocument()
  })

  test('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>انقر هنا</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  test('applies variant classes correctly', () => {
    render(<Button variant="destructive">زر خطر</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('bg-destructive')
  })

  test('applies size classes correctly', () => {
    render(<Button size="lg">زر كبير</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('h-11')
  })

  test('is disabled when disabled prop is true', () => {
    render(<Button disabled>زر معطل</Button>)
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('disabled:pointer-events-none')
  })

  test('renders as child component when asChild is true', () => {
    render(
      <Button asChild>
        <a href="/test">رابط</a>
      </Button>
    )
    expect(screen.getByRole('link')).toBeInTheDocument()
  })
})
