'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  ArrowLeft, TrendingUp, DollarSign, Package,
  Users, BarChart3, Download, Star, AlertTriangle,
  Calendar, FileText, PieChart, Activity, Target
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
// import { ExcelExporter } from '@/lib/excel-export'
import { realDataManager, Sale, Product, Customer } from '@/lib/real-storage'

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'year'>('today')
  const [selectedReport, setSelectedReport] = useState<'overview' | 'products' | 'customers' | 'inventory'>('overview')
  const [sales, setSales] = useState<Sale[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])

  useEffect(() => {
    realDataManager.initializeData()
    loadData()
  }, [])

  const loadData = () => {
    setSales(realDataManager.getSales())
    setProducts(realDataManager.getProducts())
    setCustomers(realDataManager.getCustomers())
  }

  // حساب البيانات الحقيقية
  const getFilteredSales = () => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    return sales.filter(sale => {
      const saleDate = new Date(sale.createdAt)

      switch (selectedPeriod) {
        case 'today':
          return saleDate >= today
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          return saleDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
          return saleDate >= monthAgo
        case 'year':
          const yearAgo = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000)
          return saleDate >= yearAgo
        default:
          return true
      }
    })
  }

  const filteredSales = getFilteredSales()

  // حساب الإحصائيات
  const totalSales = filteredSales.reduce((sum, sale) => sum + sale.total, 0)
  const totalOrders = filteredSales.length
  const totalCustomers = new Set(filteredSales.map(sale => sale.customerId).filter(Boolean)).size
  const averageOrder = totalOrders > 0 ? totalSales / totalOrders : 0

  // أهم المنتجات
  const productSales = new Map<string, { name: string, sales: number, quantity: number }>()

  filteredSales.forEach(sale => {
    sale.items.forEach(item => {
      const existing = productSales.get(item.productId) || { name: item.productName, sales: 0, quantity: 0 }
      existing.sales += item.total
      existing.quantity += item.quantity
      productSales.set(item.productId, existing)
    })
  })

  const topProducts = Array.from(productSales.values())
    .sort((a, b) => b.sales - a.sales)
    .slice(0, 5)

  // أهم العملاء
  const customerSales = new Map<string, { name: string, sales: number, orders: number }>()

  filteredSales.forEach(sale => {
    if (sale.customerId) {
      const existing = customerSales.get(sale.customerId) || { name: sale.customerName, sales: 0, orders: 0 }
      existing.sales += sale.total
      existing.orders += 1
      customerSales.set(sale.customerId, existing)
    }
  })

  const topCustomers = Array.from(customerSales.values())
    .sort((a, b) => b.sales - a.sales)
    .slice(0, 5)

  // منتجات منخفضة المخزون
  const lowStockProducts = products.filter(product => product.stock <= product.minStock)

  const reportData = {
    overview: {
      totalSales,
      totalOrders,
      totalCustomers,
      averageOrder,
      topProducts
    },
    products: products.map(product => {
      const productSalesData = productSales.get(product.id) || { sales: 0, quantity: 0 }
      return {
        id: product.id,
        name: product.name,
        category: product.category || 'غير محدد',
        stock: product.stock,
        minStock: product.minStock,
        sold: productSalesData.quantity,
        revenue: productSalesData.sales,
        profit: productSalesData.sales * 0.2, // افتراض هامش ربح 20%
        status: product.stock <= product.minStock ? 'مخزون منخفض' : 'متوفر'
      }
    }),
    customers: topCustomers.map(customer => ({
      name: customer.name,
      orders: customer.orders,
      totalSpent: customer.sales,
      averageOrder: customer.orders > 0 ? customer.sales / customer.orders : 0
    })),
    inventory: {
      totalProducts: products.length,
      lowStockCount: lowStockProducts.length,
      totalValue: products.reduce((sum, product) => sum + (product.price * product.stock), 0),
      lowStockProducts: lowStockProducts.map(product => ({
        name: product.name,
        stock: product.stock,
        minStock: product.minStock,
        status: 'مخزون منخفض'
      }))
    }
  }

  // وظائف التصدير
  const exportToCSV = (data: any[], filename: string) => {
    if (data.length === 0) {
      toast({
        title: "تحذير",
        description: "لا توجد بيانات للتصدير",
        variant: "destructive"
      })
      return
    }

    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: "تم التصدير",
      description: `تم تصدير ${data.length} عنصر إلى ملف CSV`
    })
  }

  // تصدير Excel متقدم - ستكون متاحة في التحديث القادم
  const exportToExcel = async (type: string) => {
    toast({
      title: "قريباً",
      description: "ميزة تصدير Excel ستكون متاحة في التحديث القادم",
      variant: "default"
    })
  }

  const periods = [
    { key: 'today', label: 'اليوم' },
    { key: 'week', label: 'هذا الأسبوع' },
    { key: 'month', label: 'هذا الشهر' },
    { key: 'year', label: 'هذا العام' }
  ]

  const reports = [
    { key: 'overview', label: 'نظرة عامة', icon: BarChart3 },
    { key: 'products', label: 'تقرير المنتجات', icon: Package },
    { key: 'customers', label: 'تقرير العملاء', icon: Users },
    { key: 'inventory', label: 'تقرير المخزون', icon: AlertTriangle },
    { key: 'financial', label: 'التقرير المالي', icon: DollarSign },
    { key: 'trends', label: 'الاتجاهات', icon: TrendingUp }
  ]

  // مكون مخطط بياني بسيط
  const SimpleBarChart = ({ data, title }: { data: any[], title: string }) => {
    const maxValue = Math.max(...data.map(item => item.value || item.sales || item.revenue || 0))

    return (
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">{title}</h4>
        <div className="space-y-3">
          {data.slice(0, 5).map((item, index) => {
            const value = item.value || item.sales || item.revenue || 0
            const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0

            return (
              <div key={index} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">{item.name || item.category || item.label}</span>
                  <span className="font-medium">{formatCurrency(value)}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  // حساب البيانات المالية
  const financialData = {
    totalRevenue: totalSales,
    totalCost: filteredSales.reduce((sum, sale) => {
      return sum + sale.items.reduce((itemSum, item) => {
        const product = products.find(p => p.id === item.productId)
        return itemSum + (product ? product.cost * item.quantity : 0)
      }, 0)
    }, 0),
    get totalProfit() { return this.totalRevenue - this.totalCost },
    get profitMargin() { return this.totalRevenue > 0 ? (this.totalProfit / this.totalRevenue) * 100 : 0 }
  }

  // حساب بيانات الاتجاهات
  const trendsData = {
    salesGrowth: calculateGrowth('sales'),
    customerGrowth: calculateGrowth('customers'),
    productPerformance: topProducts.map(p => ({ name: p.name, sales: p.sales }))
  }

  function calculateGrowth(type: 'sales' | 'customers') {
    // حساب نمو بسيط مقارنة بالفترة السابقة
    const currentPeriodSales = filteredSales.length
    const previousPeriodSales = Math.max(1, currentPeriodSales - 5) // محاكاة بيانات سابقة

    if (type === 'sales') {
      return ((currentPeriodSales - previousPeriodSales) / previousPeriodSales) * 100
    } else {
      const currentCustomers = new Set(filteredSales.map(s => s.customerId)).size
      const previousCustomers = Math.max(1, currentCustomers - 2)
      return ((currentCustomers - previousCustomers) / previousCustomers) * 100
    }
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col" dir="rtl">
      {/* Header */}
      <div className="bg-white shadow-sm border-b p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">التقارير والإحصائيات</h1>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => exportToCSV(reportData[selectedReport as keyof typeof reportData] as any[], `تقرير_${selectedReport}_${new Date().toISOString().split('T')[0]}`)}
            >
              <Download className="h-4 w-4" />
              تصدير CSV
            </Button>
            <Button
              variant="default"
              className="flex items-center gap-2"
              onClick={() => exportToExcel(selectedReport)}
            >
              <Download className="h-4 w-4" />
              تصدير Excel
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white border-b p-4 flex-shrink-0">
        <div className="flex items-center gap-4">
          {/* Period Filter */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">الفترة:</span>
            <div className="flex gap-1">
              {periods.map(period => (
                <Button
                  key={period.key}
                  variant={selectedPeriod === period.key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedPeriod(period.key as any)}
                >
                  {period.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Report Type Filter */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">نوع التقرير:</span>
            <div className="flex gap-1">
              {reports.map(report => {
                const Icon = report.icon
                return (
                  <Button
                    key={report.key}
                    variant={selectedReport === report.key ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedReport(report.key as any)}
                    className="flex items-center gap-2"
                  >
                    <Icon className="h-4 w-4" />
                    {report.label}
                  </Button>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {selectedReport === 'overview' && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">إجمالي المبيعات</p>
                      <p className="text-2xl font-bold text-green-600">
                        {formatCurrency(reportData.overview.totalSales)}
                      </p>
                    </div>
                    <div className="bg-green-100 p-3 rounded-full">
                      <DollarSign className="h-6 w-6 text-green-600" />
                    </div>
                  </div>
                  <div className="flex items-center mt-2 text-sm">
                    <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
                    <span className="text-green-500">+27.5%</span>
                    <span className="text-gray-500 mr-2">من الأسبوع الماضي</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">عدد الطلبات</p>
                      <p className="text-2xl font-bold text-blue-600">
                        {reportData.overview.totalOrders}
                      </p>
                    </div>
                    <div className="bg-blue-100 p-3 rounded-full">
                      <Package className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                  <div className="flex items-center mt-2 text-sm">
                    <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
                    <span className="text-green-500">+12.3%</span>
                    <span className="text-gray-500 mr-2">من الأسبوع الماضي</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">عدد العملاء</p>
                      <p className="text-2xl font-bold text-purple-600">
                        {reportData.overview.totalCustomers}
                      </p>
                    </div>
                    <div className="bg-purple-100 p-3 rounded-full">
                      <Users className="h-6 w-6 text-purple-600" />
                    </div>
                  </div>
                  <div className="flex items-center mt-2 text-sm">
                    <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
                    <span className="text-green-500">+8.1%</span>
                    <span className="text-gray-500 mr-2">من الأسبوع الماضي</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">متوسط الطلب</p>
                      <p className="text-2xl font-bold text-orange-600">
                        {formatCurrency(reportData.overview.averageOrder)}
                      </p>
                    </div>
                    <div className="bg-orange-100 p-3 rounded-full">
                      <BarChart3 className="h-6 w-6 text-orange-600" />
                    </div>
                  </div>
                  <div className="flex items-center mt-2 text-sm">
                    <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
                    <span className="text-green-500">+15.2%</span>
                    <span className="text-gray-500 mr-2">من الأسبوع الماضي</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Top Products */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  أفضل المنتجات مبيعاً
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {reportData.overview.topProducts.map((product, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="bg-blue-100 text-blue-600 rounded-full w-8 h-8 flex items-center justify-center font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{product.name}</div>
                          <div className="text-sm text-gray-600">
                            الكمية المباعة: {product.quantity}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-green-600">
                          {formatCurrency(product.sales)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {selectedReport === 'products' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>تقرير المنتجات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="text-right p-3 font-medium text-gray-700">المنتج</th>
                        <th className="text-right p-3 font-medium text-gray-700">الفئة</th>
                        <th className="text-right p-3 font-medium text-gray-700">المخزون</th>
                        <th className="text-right p-3 font-medium text-gray-700">المباع</th>
                        <th className="text-right p-3 font-medium text-gray-700">الإيرادات</th>
                        <th className="text-right p-3 font-medium text-gray-700">الربح</th>
                        <th className="text-right p-3 font-medium text-gray-700">الحالة</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.products.map((product) => (
                        <tr key={product.id} className="border-b hover:bg-gray-50">
                          <td className="p-3 font-medium">{product.name}</td>
                          <td className="p-3 text-gray-600">{product.category}</td>
                          <td className="p-3">{product.stock} {product.id === '1' ? 'طن' : product.id === '2' ? 'لوح' : 'متر'}</td>
                          <td className="p-3">{product.sold}</td>
                          <td className="p-3 text-green-600 font-medium">{formatCurrency(product.revenue)}</td>
                          <td className="p-3 text-blue-600 font-medium">{formatCurrency(product.profit)}</td>
                          <td className="p-3">
                            <Badge className={product.status === 'متوفر' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                              {product.status}
                            </Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {selectedReport === 'customers' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>تقرير العملاء</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="text-right p-3 font-medium text-gray-700">العميل</th>
                        <th className="text-right p-3 font-medium text-gray-700">الهاتف</th>
                        <th className="text-right p-3 font-medium text-gray-700">إجمالي المشتريات</th>
                        <th className="text-right p-3 font-medium text-gray-700">عدد الطلبات</th>
                        <th className="text-right p-3 font-medium text-gray-700">آخر طلب</th>
                        <th className="text-right p-3 font-medium text-gray-700">النوع</th>
                        <th className="text-right p-3 font-medium text-gray-700">الديون</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.customers.map((customer) => (
                        <tr key={customer.id} className="border-b hover:bg-gray-50">
                          <td className="p-3 font-medium">{customer.name}</td>
                          <td className="p-3 text-gray-600">{customer.phone}</td>
                          <td className="p-3 text-green-600 font-medium">{formatCurrency(customer.totalPurchases)}</td>
                          <td className="p-3">{customer.ordersCount}</td>
                          <td className="p-3 text-gray-600">{customer.lastOrder.toLocaleDateString('ar-SA')}</td>
                          <td className="p-3">
                            <Badge className={customer.status === 'عميل مميز' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'}>
                              {customer.status}
                            </Badge>
                          </td>
                          <td className="p-3">
                            <span className={customer.debt > 0 ? 'text-red-600 font-medium' : 'text-green-600'}>
                              {customer.debt > 0 ? formatCurrency(customer.debt) : 'لا يوجد'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {selectedReport === 'inventory' && (
          <div className="space-y-6">
            {/* Inventory Alerts */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">مخزون منخفض</p>
                      <p className="text-2xl font-bold text-yellow-600">2</p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">نفد المخزون</p>
                      <p className="text-2xl font-bold text-red-600">1</p>
                    </div>
                    <Package className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">قيمة المخزون</p>
                      <p className="text-2xl font-bold text-green-600">{formatCurrency(850000)}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Low Stock Items */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                  منتجات تحتاج إعادة تموين
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div>
                      <div className="font-medium">صاج مجلفن 2 مم</div>
                      <div className="text-sm text-gray-600">المخزون الحالي: 195 لوح - الحد الأدنى: 200 لوح</div>
                    </div>
                    <Badge className="bg-yellow-100 text-yellow-800">مخزون منخفض</Badge>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div>
                      <div className="font-medium">مسامير حديد 10 سم</div>
                      <div className="text-sm text-gray-600">المخزون الحالي: 45 كيلو - الحد الأدنى: 100 كيلو</div>
                    </div>
                    <Badge className="bg-yellow-100 text-yellow-800">مخزون منخفض</Badge>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div>
                      <div className="font-medium">سلك حديد 6 مم</div>
                      <div className="text-sm text-gray-600">المخزون الحالي: 0 كيلو - الحد الأدنى: 50 كيلو</div>
                    </div>
                    <Badge className="bg-red-100 text-red-800">نفد المخزون</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
