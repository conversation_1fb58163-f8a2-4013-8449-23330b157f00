'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { realDataManager, Product, Customer, Sale } from '@/lib/real-storage'
import { 
  Search, 
  Plus, 
  Minus, 
  ShoppingCart, 
  User, 
  CreditCard,
  Trash2,
  Calculator,
  Receipt,
  UserPlus,
  Printer,
  CheckCircle
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import PrintManager from '@/components/print/PrintManager'
import InvoicePrint from '@/components/print/InvoicePrint'
import { BarcodeScanButton } from '@/components/barcode/BarcodeScanner'

interface CartItem {
  productId: string
  name: string
  price: number
  quantity: number
  stock: number
}

export default function POSPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [cart, setCart] = useState<CartItem[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [paymentModalOpen, setPaymentModalOpen] = useState(false)
  const [customerModalOpen, setCustomerModalOpen] = useState(false)
  const [paymentType, setPaymentType] = useState<'cash' | 'credit' | 'partial'>('cash')
  const [paidAmount, setPaidAmount] = useState('')
  const [discount, setDiscount] = useState('')
  const [notes, setNotes] = useState('')
  const [printModalOpen, setPrintModalOpen] = useState(false)
  const [lastSale, setLastSale] = useState<Sale | null>(null)

  // New customer form
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    phone: '',
    company: '',
    address: '',
    creditLimit: ''
  })

  useEffect(() => {
    realDataManager.initializeData()
    loadData()
  }, [])

  const loadData = () => {
    setProducts(realDataManager.getProducts())
    setCustomers(realDataManager.getCustomers())
  }

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.barcode && product.barcode.includes(searchTerm))
  )

  const addToCart = (product: Product) => {
    if (product.stock <= 0) {
      toast({
        title: "خطأ",
        description: "المنتج غير متوفر في المخزون",
        variant: "destructive"
      })
      return
    }

    const existingItem = cart.find(item => item.productId === product.id)
    
    if (existingItem) {
      if (existingItem.quantity >= product.stock) {
        toast({
          title: "تحذير",
          description: "الكمية المطلوبة تتجاوز المخزون المتاح",
          variant: "destructive"
        })
        return
      }
      
      setCart(cart.map(item =>
        item.productId === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ))
    } else {
      setCart([...cart, {
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        stock: product.stock
      }])
    }
  }

  // البحث بالباركود
  const handleBarcodeScanned = (barcode: string) => {
    const product = products.find(p => p.barcode === barcode)
    if (product) {
      addToCart(product)
      toast({
        title: "تم إضافة المنتج",
        description: `تم إضافة ${product.name} إلى السلة`
      })
    } else {
      toast({
        title: "منتج غير موجود",
        description: `لا يوجد منتج بالباركود: ${barcode}`,
        variant: "destructive"
      })
    }
  }

  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId)
      return
    }

    const product = products.find(p => p.id === productId)
    if (product && newQuantity > product.stock) {
      toast({
        title: "تحذير",
        description: "الكمية المطلوبة تتجاوز المخزون المتاح",
        variant: "destructive"
      })
      return
    }

    setCart(cart.map(item =>
      item.productId === productId
        ? { ...item, quantity: newQuantity }
        : item
    ))
  }

  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.productId !== productId))
  }

  const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  const discountAmount = parseFloat(discount) || 0
  const tax = (subtotal - discountAmount) * 0.15 // 15% ضريبة
  const total = subtotal - discountAmount + tax

  const addNewCustomer = () => {
    if (!newCustomer.name || !newCustomer.phone) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم العميل ورقم الهاتف",
        variant: "destructive"
      })
      return
    }

    try {
      const customer = realDataManager.addCustomer({
        name: newCustomer.name,
        phone: newCustomer.phone,
        company: newCustomer.company,
        address: newCustomer.address,
        creditLimit: parseFloat(newCustomer.creditLimit) || 0,
        status: 'active'
      })

      setCustomers([...customers, customer])
      setSelectedCustomer(customer)
      setNewCustomer({ name: '', phone: '', company: '', address: '', creditLimit: '' })
      setCustomerModalOpen(false)
      
      toast({
        title: "تم بنجاح",
        description: "تم إضافة العميل الجديد"
      })
    } catch (error) {
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء إضافة العميل",
        variant: "destructive"
      })
    }
  }

  const completeSale = () => {
    if (cart.length === 0) {
      toast({
        title: "خطأ",
        description: "السلة فارغة",
        variant: "destructive"
      })
      return
    }

    const paidAmountNum = parseFloat(paidAmount) || 0
    
    if (paymentType === 'cash' && paidAmountNum < total) {
      toast({
        title: "خطأ",
        description: "المبلغ المدفوع أقل من إجمالي الفاتورة",
        variant: "destructive"
      })
      return
    }

    if (paymentType === 'partial' && paidAmountNum >= total) {
      toast({
        title: "تحذير",
        description: "المبلغ المدفوع يغطي كامل الفاتورة، سيتم تحويلها إلى دفع نقدي",
      })
      setPaymentType('cash')
    }

    try {
      const sale = realDataManager.addSale({
        customerId: selectedCustomer?.id,
        customerName: selectedCustomer?.name || 'عميل نقدي',
        items: cart.map(item => ({
          id: Date.now().toString() + Math.random().toString(36).substr(2),
          productId: item.productId,
          productName: item.name,
          quantity: item.quantity,
          unitPrice: item.price,
          total: item.price * item.quantity
        })),
        subtotal,
        discount: discountAmount,
        tax,
        total,
        paymentType,
        paidAmount: paidAmountNum,
        remainingAmount: total - paidAmountNum,
        status: paymentType === 'cash' ? 'completed' : 'pending',
        notes
      })

      // حفظ بيانات البيع للطباعة
      setLastSale(sale)

      // Clear cart and reset form
      setCart([])
      setSelectedCustomer(null)
      setPaidAmount('')
      setDiscount('')
      setNotes('')
      setPaymentType('cash')
      setPaymentModalOpen(false)

      // Reload data to reflect stock changes
      loadData()

      // عرض خيار الطباعة
      toast({
        title: "تم إتمام البيع بنجاح",
        description: "هل تريد طباعة الفاتورة؟",
        action: (
          <Button
            size="sm"
            onClick={() => setPrintModalOpen(true)}
            className="flex items-center gap-1"
          >
            <Printer className="h-3 w-3" />
            طباعة
          </Button>
        )
      })

      toast({
        title: "تم بنجاح",
        description: `تم إتمام البيع - رقم الفاتورة: ${sale.id.slice(-6)}`
      })
    } catch (error) {
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء إتمام البيع",
        variant: "destructive"
      })
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Products Section */}
        <div className="flex-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                المنتجات
              </CardTitle>
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث عن منتج..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
                <BarcodeScanButton onScan={handleBarcodeScanned} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                {filteredProducts.map((product) => (
                  <Card 
                    key={product.id} 
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => addToCart(product)}
                  >
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium text-sm">{product.name}</h3>
                        <Badge variant={product.stock > 0 ? "default" : "destructive"}>
                          {product.stock}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-600 mb-2">{product.category}</p>
                      <p className="font-bold text-green-600">{product.price.toLocaleString()} ر.ي</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Cart Section */}
        <div className="w-full lg:w-96">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Receipt className="h-5 w-5" />
                  السلة ({cart.length})
                </span>
                {cart.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCart([])}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Customer Selection */}
              <div className="space-y-2">
                <Label>العميل</Label>
                <div className="flex gap-2">
                  <Select
                    value={selectedCustomer?.id || 'cash-customer'}
                    onValueChange={(value) => {
                      if (value === 'cash-customer') {
                        setSelectedCustomer(null)
                      } else {
                        const customer = customers.find(c => c.id === value)
                        setSelectedCustomer(customer || null)
                      }
                    }}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="اختر عميل..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cash-customer">عميل نقدي</SelectItem>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.name} - {customer.phone}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Dialog open={customerModalOpen} onOpenChange={setCustomerModalOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="icon">
                        <UserPlus className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>إضافة عميل جديد</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label>اسم العميل *</Label>
                          <Input
                            value={newCustomer.name}
                            onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                            placeholder="أدخل اسم العميل"
                          />
                        </div>
                        <div>
                          <Label>رقم الهاتف *</Label>
                          <Input
                            value={newCustomer.phone}
                            onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                            placeholder="أدخل رقم الهاتف"
                          />
                        </div>
                        <div>
                          <Label>الشركة</Label>
                          <Input
                            value={newCustomer.company}
                            onChange={(e) => setNewCustomer({...newCustomer, company: e.target.value})}
                            placeholder="أدخل اسم الشركة"
                          />
                        </div>
                        <div>
                          <Label>العنوان</Label>
                          <Input
                            value={newCustomer.address}
                            onChange={(e) => setNewCustomer({...newCustomer, address: e.target.value})}
                            placeholder="أدخل العنوان"
                          />
                        </div>
                        <div>
                          <Label>الحد الائتماني</Label>
                          <Input
                            type="number"
                            value={newCustomer.creditLimit}
                            onChange={(e) => setNewCustomer({...newCustomer, creditLimit: e.target.value})}
                            placeholder="0"
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button onClick={addNewCustomer} className="flex-1">
                            إضافة العميل
                          </Button>
                          <Button 
                            variant="outline" 
                            onClick={() => setCustomerModalOpen(false)}
                          >
                            إلغاء
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>

              <Separator />

              {/* Cart Items */}
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {cart.length === 0 ? (
                  <p className="text-center text-gray-500 py-8">السلة فارغة</p>
                ) : (
                  cart.map((item) => (
                    <div key={item.productId} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex-1">
                        <p className="font-medium text-sm">{item.name}</p>
                        <p className="text-xs text-gray-600">{item.price.toLocaleString()} ر.ي</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => updateQuantity(item.productId, item.quantity - 1)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="w-8 text-center text-sm">{item.quantity}</span>
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => updateQuantity(item.productId, item.quantity + 1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => removeFromCart(item.productId)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {cart.length > 0 && (
                <>
                  <Separator />
                  
                  {/* Discount */}
                  <div>
                    <Label>الخصم</Label>
                    <Input
                      type="number"
                      value={discount}
                      onChange={(e) => setDiscount(e.target.value)}
                      placeholder="0"
                    />
                  </div>

                  {/* Totals */}
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>المجموع الفرعي:</span>
                      <span>{subtotal.toLocaleString()} ر.ي</span>
                    </div>
                    {discountAmount > 0 && (
                      <div className="flex justify-between text-red-600">
                        <span>الخصم:</span>
                        <span>-{discountAmount.toLocaleString()} ر.ي</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span>الضريبة (15%):</span>
                      <span>{tax.toLocaleString()} ر.ي</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between font-bold text-lg">
                      <span>الإجمالي:</span>
                      <span>{total.toLocaleString()} ر.ي</span>
                    </div>
                  </div>

                  {/* Payment Button */}
                  <Dialog open={paymentModalOpen} onOpenChange={setPaymentModalOpen}>
                    <DialogTrigger asChild>
                      <Button className="w-full" size="lg">
                        <CreditCard className="mr-2 h-4 w-4" />
                        إتمام البيع
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>إتمام البيع</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label>نوع الدفع</Label>
                          <Select value={paymentType} onValueChange={(value: any) => setPaymentType(value)}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="cash">نقدي</SelectItem>
                              <SelectItem value="credit">آجل</SelectItem>
                              <SelectItem value="partial">دفع جزئي</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {(paymentType === 'cash' || paymentType === 'partial') && (
                          <div>
                            <Label>المبلغ المدفوع</Label>
                            <Input
                              type="number"
                              value={paidAmount}
                              onChange={(e) => setPaidAmount(e.target.value)}
                              placeholder={total.toString()}
                            />
                          </div>
                        )}

                        <div>
                          <Label>ملاحظات</Label>
                          <Textarea
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                            placeholder="ملاحظات إضافية..."
                          />
                        </div>

                        <div className="flex gap-2">
                          <Button onClick={completeSale} className="flex-1">
                            تأكيد البيع
                          </Button>
                          <Button 
                            variant="outline" 
                            onClick={() => setPaymentModalOpen(false)}
                          >
                            إلغاء
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  {/* مودال الطباعة */}
                  <Dialog open={printModalOpen} onOpenChange={setPrintModalOpen}>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>طباعة الفاتورة</DialogTitle>
                      </DialogHeader>
                      {lastSale && (
                        <PrintManager
                          filename={`pos-receipt-${lastSale.id.slice(-6)}`}
                          title={`فاتورة نقطة البيع رقم ${lastSale.id.slice(-6)}`}
                        >
                          <InvoicePrint
                            invoice={{
                              id: lastSale.id,
                              invoiceNumber: `POS-${lastSale.id.slice(-6)}`,
                              customerName: lastSale.customerName,
                              customerEmail: '',
                              customerPhone: '',
                              customerAddress: '',
                              issueDate: new Date(lastSale.createdAt).toISOString(),
                              dueDate: new Date(lastSale.createdAt).toISOString(),
                              items: lastSale.items.map(item => ({
                                id: item.id,
                                description: item.productName,
                                quantity: item.quantity,
                                unitPrice: item.unitPrice,
                                total: item.total
                              })),
                              subtotal: lastSale.subtotal,
                              taxAmount: lastSale.tax,
                              discountAmount: lastSale.discount,
                              total: lastSale.total,
                              notes: lastSale.notes || '',
                              status: lastSale.status
                            }}
                          />
                        </PrintManager>
                      )}
                    </DialogContent>
                  </Dialog>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
