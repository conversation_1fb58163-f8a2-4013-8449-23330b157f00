'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { 
  Download, Upload, RefreshCw, Trash2, Database, 
  Shield, Clock, HardDrive, AlertTriangle, CheckCircle,
  FileText, Calendar
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { 
  createBackup, 
  exportBackup, 
  importBackupFromFile, 
  restoreFromBackup,
  getAutoBackups,
  deleteAutoBackup,
  getBackupStats,
  createAutoBackup,
  BackupData
} from '@/lib/backup-utils'

export default function BackupManager() {
  const [autoBackups, setAutoBackups] = useState<any[]>([])
  const [backupStats, setBackupStats] = useState<any>(null)
  const [showRestoreDialog, setShowRestoreDialog] = useState(false)
  const [selectedBackup, setSelectedBackup] = useState<any>(null)
  const [isCreatingBackup, setIsCreatingBackup] = useState(false)
  const [isRestoring, setIsRestoring] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    loadBackupData()
  }, [])

  const loadBackupData = () => {
    setAutoBackups(getAutoBackups())
    setBackupStats(getBackupStats())
  }

  // إنشاء نسخة احتياطية يدوية
  const handleCreateBackup = async () => {
    setIsCreatingBackup(true)
    try {
      const backup = createBackup('نسخة احتياطية يدوية')
      exportBackup(backup)
      
      toast({
        title: "تم بنجاح",
        description: "تم إنشاء وتصدير النسخة الاحتياطية بنجاح"
      })
      
      loadBackupData()
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إنشاء النسخة الاحتياطية",
        variant: "destructive"
      })
    } finally {
      setIsCreatingBackup(false)
    }
  }

  // إنشاء نسخة احتياطية تلقائية
  const handleCreateAutoBackup = async () => {
    try {
      createAutoBackup()
      loadBackupData()
      
      toast({
        title: "تم بنجاح",
        description: "تم إنشاء النسخة الاحتياطية التلقائية"
      })
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إنشاء النسخة الاحتياطية التلقائية",
        variant: "destructive"
      })
    }
  }

  // استيراد نسخة احتياطية
  const handleImportBackup = () => {
    fileInputRef.current?.click()
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      const backup = await importBackupFromFile(file)
      setSelectedBackup(backup)
      setShowRestoreDialog(true)
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في قراءة ملف النسخة الاحتياطية",
        variant: "destructive"
      })
    }
  }

  // استعادة البيانات
  const handleRestore = async (clearExisting: boolean = true) => {
    if (!selectedBackup) return

    setIsRestoring(true)
    try {
      restoreFromBackup(selectedBackup, { clearExisting })
      
      toast({
        title: "تم بنجاح",
        description: "تم استعادة البيانات بنجاح. سيتم إعادة تحميل الصفحة."
      })
      
      // إعادة تحميل الصفحة بعد ثانيتين
      setTimeout(() => {
        window.location.reload()
      }, 2000)
      
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في استعادة البيانات",
        variant: "destructive"
      })
    } finally {
      setIsRestoring(false)
      setShowRestoreDialog(false)
    }
  }

  // حذف نسخة احتياطية
  const handleDeleteBackup = (backupId: string) => {
    if (deleteAutoBackup(backupId)) {
      loadBackupData()
      toast({
        title: "تم بنجاح",
        description: "تم حذف النسخة الاحتياطية"
      })
    } else {
      toast({
        title: "خطأ",
        description: "فشل في حذف النسخة الاحتياطية",
        variant: "destructive"
      })
    }
  }

  // تنسيق حجم الملف
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      {/* إحصائيات النسخ الاحتياطي */}
      {backupStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Database className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-600">النسخ الاحتياطية</p>
                  <p className="text-2xl font-bold">{backupStats.totalAutoBackups}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <HardDrive className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-sm text-gray-600">الحجم الإجمالي</p>
                  <p className="text-2xl font-bold">{formatFileSize(backupStats.totalBackupSize)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-8 w-8 text-orange-500" />
                <div>
                  <p className="text-sm text-gray-600">آخر نسخة</p>
                  <p className="text-sm font-bold">
                    {backupStats.latestBackup 
                      ? new Date(backupStats.latestBackup.timestamp).toLocaleDateString('ar-SA')
                      : 'لا يوجد'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Shield className="h-8 w-8 text-purple-500" />
                <div>
                  <p className="text-sm text-gray-600">آخر استعادة</p>
                  <p className="text-sm font-bold">
                    {backupStats.lastRestore 
                      ? new Date(backupStats.lastRestore.timestamp).toLocaleDateString('ar-SA')
                      : 'لا يوجد'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* أزرار التحكم */}
      <Card>
        <CardHeader>
          <CardTitle>إدارة النسخ الاحتياطي</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button 
              onClick={handleCreateBackup} 
              disabled={isCreatingBackup}
              className="flex items-center gap-2"
            >
              {isCreatingBackup ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              إنشاء نسخة احتياطية
            </Button>

            <Button 
              onClick={handleCreateAutoBackup}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              نسخة تلقائية
            </Button>

            <Button 
              onClick={handleImportBackup}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              استيراد نسخة احتياطية
            </Button>

            <Button 
              onClick={loadBackupData}
              variant="ghost"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              تحديث
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* قائمة النسخ الاحتياطية */}
      <Card>
        <CardHeader>
          <CardTitle>النسخ الاحتياطية المحفوظة</CardTitle>
        </CardHeader>
        <CardContent>
          {autoBackups.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Database className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p>لا توجد نسخ احتياطية محفوظة</p>
              <p className="text-sm">قم بإنشاء نسخة احتياطية أولاً</p>
            </div>
          ) : (
            <div className="space-y-3">
              {autoBackups.map((backup) => (
                <div key={backup.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="font-medium">{backup.description}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(backup.timestamp).toLocaleString('ar-SA')}
                        </span>
                        <span>الحجم: {formatFileSize(backup.size)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setSelectedBackup(backup.data)
                        setShowRestoreDialog(true)
                      }}
                    >
                      استعادة
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => exportBackup(backup.data, `backup-${backup.id}.json`)}
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDeleteBackup(backup.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* مودال استعادة البيانات */}
      <Dialog open={showRestoreDialog} onOpenChange={setShowRestoreDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              تأكيد استعادة البيانات
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-sm text-yellow-800">
                <strong>تحذير:</strong> ستؤدي هذه العملية إلى استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.
                هذه العملية لا يمكن التراجع عنها.
              </p>
            </div>
            
            {selectedBackup && (
              <div className="space-y-2">
                <p><strong>تاريخ النسخة الاحتياطية:</strong> {new Date(selectedBackup.timestamp).toLocaleString('ar-SA')}</p>
                <p><strong>عدد السجلات:</strong> {selectedBackup.metadata.totalRecords}</p>
                <p><strong>الوصف:</strong> {selectedBackup.metadata.description}</p>
              </div>
            )}

            <div className="flex gap-3 pt-4">
              <Button
                onClick={() => handleRestore(true)}
                disabled={isRestoring}
                className="flex items-center gap-2"
              >
                {isRestoring ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                تأكيد الاستعادة
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowRestoreDialog(false)}
                disabled={isRestoring}
              >
                إلغاء
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* حقل استيراد الملف المخفي */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".json"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />
    </div>
  )
}
