// نظام إدارة البيانات المحلية
export interface Product {
  id: string
  name: string
  description: string
  category: string
  price: number
  cost: number
  stock: number
  minStock: number
  barcode?: string
  unit: string
  supplier: string
  location: string
  image?: string
  createdAt: Date
  updatedAt: Date
}

export interface Customer {
  id: string
  name: string
  company?: string
  phone: string
  email?: string
  address?: string
  type: 'individual' | 'company'
  status: 'active' | 'inactive'
  creditLimit: number
  currentDebt: number
  totalPurchases: number
  lastPurchase?: Date
  createdAt: Date
  updatedAt: Date
}

export interface Sale {
  id: string
  customerId?: string
  customerName: string
  items: SaleItem[]
  subtotal: number
  discount: number
  tax: number
  total: number
  paymentType: 'cash' | 'credit' | 'partial'
  paidAmount: number
  remainingAmount: number
  status: 'completed' | 'pending' | 'cancelled'
  notes?: string
  createdAt: Date
  updatedAt: Date
}

export interface SaleItem {
  productId: string
  productName: string
  quantity: number
  price: number
  total: number
}

export interface Return {
  id: string
  saleId: string
  customerId?: string
  customerName: string
  items: ReturnItem[]
  total: number
  reason: string
  status: 'completed' | 'pending'
  createdAt: Date
}

export interface ReturnItem {
  productId: string
  productName: string
  quantity: number
  price: number
  total: number
}

export interface Supplier {
  id: string
  name: string
  contactPerson: string
  phone: string
  email?: string
  address?: string
  category: string
  status: 'active' | 'inactive'
  paymentTerms: string
  rating: number
  totalOrders: number
  totalValue: number
  lastOrder?: Date
  createdAt: Date
  updatedAt: Date
}

export interface StockMovement {
  id: string
  productId: string
  type: 'in' | 'out' | 'adjustment'
  quantity: number
  reason: string
  reference?: string
  createdAt: Date
}

// وظائف إدارة البيانات
class DataManager {
  private getStorageKey(type: string): string {
    return `steel_trade_${type}`
  }

  // وظائف عامة للتخزين
  private getData<T>(type: string): T[] {
    if (typeof window === 'undefined') return []
    const data = localStorage.getItem(this.getStorageKey(type))
    return data ? JSON.parse(data) : []
  }

  private setData<T>(type: string, data: T[]): void {
    if (typeof window === 'undefined') return
    localStorage.setItem(this.getStorageKey(type), JSON.stringify(data))
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // إدارة المنتجات
  getProducts(): Product[] {
    return this.getData<Product>('products')
  }

  addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Product {
    const products = this.getProducts()
    const newProduct: Product = {
      ...product,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    products.push(newProduct)
    this.setData('products', products)
    return newProduct
  }

  updateProduct(id: string, updates: Partial<Product>): Product | null {
    const products = this.getProducts()
    const index = products.findIndex(p => p.id === id)
    if (index === -1) return null
    
    products[index] = { ...products[index], ...updates, updatedAt: new Date() }
    this.setData('products', products)
    return products[index]
  }

  deleteProduct(id: string): boolean {
    const products = this.getProducts()
    const filteredProducts = products.filter(p => p.id !== id)
    if (filteredProducts.length === products.length) return false
    
    this.setData('products', filteredProducts)
    return true
  }

  updateStock(productId: string, quantity: number, type: 'in' | 'out' | 'adjustment', reason: string, reference?: string): boolean {
    const products = this.getProducts()
    const product = products.find(p => p.id === productId)
    if (!product) return false

    const oldStock = product.stock
    if (type === 'in') {
      product.stock += quantity
    } else if (type === 'out') {
      if (product.stock < quantity) return false
      product.stock -= quantity
    } else {
      product.stock = quantity
    }

    product.updatedAt = new Date()
    this.setData('products', products)

    // تسجيل حركة المخزون
    this.addStockMovement({
      productId,
      type,
      quantity: type === 'adjustment' ? quantity - oldStock : quantity,
      reason,
      reference
    })

    return true
  }

  // إدارة العملاء
  getCustomers(): Customer[] {
    return this.getData<Customer>('customers')
  }

  addCustomer(customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Customer {
    const customers = this.getCustomers()
    const newCustomer: Customer = {
      ...customer,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    customers.push(newCustomer)
    this.setData('customers', customers)
    return newCustomer
  }

  updateCustomer(id: string, updates: Partial<Customer>): Customer | null {
    const customers = this.getCustomers()
    const index = customers.findIndex(c => c.id === id)
    if (index === -1) return null
    
    customers[index] = { ...customers[index], ...updates, updatedAt: new Date() }
    this.setData('customers', customers)
    return customers[index]
  }

  updateCustomerDebt(customerId: string, amount: number): boolean {
    const customers = this.getCustomers()
    const customer = customers.find(c => c.id === customerId)
    if (!customer) return false

    customer.currentDebt += amount
    customer.updatedAt = new Date()
    this.setData('customers', customers)
    return true
  }

  // إدارة المبيعات
  getSales(): Sale[] {
    return this.getData<Sale>('sales')
  }

  addSale(sale: Omit<Sale, 'id' | 'createdAt' | 'updatedAt'>): Sale | null {
    // التحقق من توفر المخزون
    for (const item of sale.items) {
      const products = this.getProducts()
      const product = products.find(p => p.id === item.productId)
      if (!product || product.stock < item.quantity) {
        return null // المخزون غير كافي
      }
    }

    const sales = this.getSales()
    const newSale: Sale = {
      ...sale,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }

    // تحديث المخزون
    for (const item of sale.items) {
      this.updateStock(item.productId, item.quantity, 'out', `بيع - فاتورة ${newSale.id}`, newSale.id)
    }

    // تحديث بيانات العميل
    if (sale.customerId) {
      const customer = this.getCustomers().find(c => c.id === sale.customerId)
      if (customer) {
        customer.totalPurchases += sale.total
        customer.lastPurchase = new Date()
        if (sale.paymentType === 'credit' || sale.remainingAmount > 0) {
          customer.currentDebt += sale.remainingAmount
        }
        this.updateCustomer(customer.id, customer)
      }
    }

    sales.push(newSale)
    this.setData('sales', sales)
    return newSale
  }

  // إدارة المرتجعات
  getReturns(): Return[] {
    return this.getData<Return>('returns')
  }

  addReturn(returnData: Omit<Return, 'id' | 'createdAt'>): Return | null {
    const sales = this.getSales()
    const sale = sales.find(s => s.id === returnData.saleId)
    if (!sale) return null

    const returns = this.getReturns()
    const newReturn: Return = {
      ...returnData,
      id: this.generateId(),
      createdAt: new Date()
    }

    // إرجاع المخزون
    for (const item of returnData.items) {
      this.updateStock(item.productId, item.quantity, 'in', `مرتجع - ${newReturn.id}`, newReturn.id)
    }

    // تحديث بيانات العميل
    if (returnData.customerId) {
      const customer = this.getCustomers().find(c => c.id === returnData.customerId)
      if (customer) {
        customer.totalPurchases -= returnData.total
        customer.currentDebt -= returnData.total
        this.updateCustomer(customer.id, customer)
      }
    }

    returns.push(newReturn)
    this.setData('returns', returns)
    return newReturn
  }

  // إدارة حركات المخزون
  getStockMovements(): StockMovement[] {
    return this.getData<StockMovement>('stock_movements')
  }

  addStockMovement(movement: Omit<StockMovement, 'id' | 'createdAt'>): StockMovement {
    const movements = this.getStockMovements()
    const newMovement: StockMovement = {
      ...movement,
      id: this.generateId(),
      createdAt: new Date()
    }
    movements.push(newMovement)
    this.setData('stock_movements', movements)
    return newMovement
  }

  // إدارة الموردين
  getSuppliers(): Supplier[] {
    return this.getData<Supplier>('suppliers')
  }

  addSupplier(supplier: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt'>): Supplier {
    const suppliers = this.getSuppliers()
    const newSupplier: Supplier = {
      ...supplier,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    suppliers.push(newSupplier)
    this.setData('suppliers', suppliers)
    return newSupplier
  }

  // تهيئة البيانات الأولية
  initializeData(): void {
    if (this.getProducts().length === 0) {
      this.initializeProducts()
    }
    if (this.getCustomers().length === 0) {
      this.initializeCustomers()
    }
    if (this.getSuppliers().length === 0) {
      this.initializeSuppliers()
    }
  }

  private initializeProducts(): void {
    const initialProducts = [
      {
        name: 'حديد تسليح 8 مم',
        description: 'حديد تسليح عالي الجودة مقاس 8 مم',
        category: 'حديد التسليح',
        price: 850,
        cost: 750,
        stock: 500,
        minStock: 100,
        barcode: '8901234567890',
        unit: 'طن',
        supplier: 'شركة الحديد المتحدة',
        location: 'المستودع الرئيسي - رف A1'
      },
      {
        name: 'حديد تسليح 10 مم',
        description: 'حديد تسليح عالي الجودة مقاس 10 مم',
        category: 'حديد التسليح',
        price: 870,
        cost: 770,
        stock: 400,
        minStock: 80,
        barcode: '8901234567891',
        unit: 'طن',
        supplier: 'شركة الحديد المتحدة',
        location: 'المستودع الرئيسي - رف A2'
      }
    ]

    initialProducts.forEach(product => this.addProduct(product))
  }

  private initializeCustomers(): void {
    const initialCustomers = [
      {
        name: 'أحمد محمد علي',
        company: 'مؤسسة البناء الحديث',
        phone: '*********',
        email: '<EMAIL>',
        address: 'صنعاء - شارع الزبيري',
        type: 'company' as const,
        status: 'active' as const,
        creditLimit: 500000,
        currentDebt: 0,
        totalPurchases: 0
      }
    ]

    initialCustomers.forEach(customer => this.addCustomer(customer))
  }

  private initializeSuppliers(): void {
    const initialSuppliers = [
      {
        name: 'شركة الحديد المتحدة',
        contactPerson: 'علي أحمد محمد',
        phone: '*********',
        email: '<EMAIL>',
        address: 'صنعاء - المنطقة الصناعية',
        category: 'حديد التسليح',
        status: 'active' as const,
        paymentTerms: '30 يوم',
        rating: 4.8,
        totalOrders: 0,
        totalValue: 0
      }
    ]

    initialSuppliers.forEach(supplier => this.addSupplier(supplier))
  }
}

export const dataManager = new DataManager()
