'use client'

import React, { forwardRef } from 'react'
import { formatCurrency } from '@/lib/utils'

interface InvoiceItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  total: number
}

interface Invoice {
  id: string
  invoiceNumber: string
  customerName: string
  customerEmail?: string
  customerPhone?: string
  customerAddress?: string
  issueDate: string
  dueDate: string
  items: InvoiceItem[]
  subtotal: number
  taxAmount: number
  discountAmount: number
  total: number
  notes?: string
  status: string
}

interface InvoicePrintProps {
  invoice: Invoice
  companyInfo?: {
    name: string
    address: string
    phone: string
    email: string
    taxNumber: string
    logo?: string
  }
}

const InvoicePrint = forwardRef<HTMLDivElement, InvoicePrintProps>(
  ({ invoice, companyInfo }, ref) => {
    const defaultCompanyInfo = {
      name: 'تجارة الحديد والمعدات',
      address: 'صنعاء، اليمن - شارع الزبيري',
      phone: '+967-1-234567',
      email: '<EMAIL>',
      taxNumber: '*********',
      ...companyInfo
    }

    return (
      <div ref={ref} className="bg-white p-8 max-w-4xl mx-auto" dir="rtl">
        {/* Header */}
        <div className="border-b-2 border-gray-300 pb-6 mb-6">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">
                {defaultCompanyInfo.name}
              </h1>
              <div className="text-gray-600 space-y-1">
                <p>{defaultCompanyInfo.address}</p>
                <p>هاتف: {defaultCompanyInfo.phone}</p>
                <p>إيميل: {defaultCompanyInfo.email}</p>
                <p>الرقم الضريبي: {defaultCompanyInfo.taxNumber}</p>
              </div>
            </div>
            <div className="text-left">
              <h2 className="text-2xl font-bold text-blue-600 mb-2">فاتورة</h2>
              <div className="text-gray-600">
                <p>رقم الفاتورة: <span className="font-semibold">{invoice.invoiceNumber}</span></p>
                <p>تاريخ الإصدار: <span className="font-semibold">{new Date(invoice.issueDate).toLocaleDateString('ar-SA')}</span></p>
                <p>تاريخ الاستحقاق: <span className="font-semibold">{new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</span></p>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Info */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">معلومات العميل:</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="font-semibold text-gray-800">{invoice.customerName}</p>
            {invoice.customerEmail && <p className="text-gray-600">إيميل: {invoice.customerEmail}</p>}
            {invoice.customerPhone && <p className="text-gray-600">هاتف: {invoice.customerPhone}</p>}
            {invoice.customerAddress && <p className="text-gray-600">العنوان: {invoice.customerAddress}</p>}
          </div>
        </div>

        {/* Items Table */}
        <div className="mb-8">
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 p-3 text-right font-semibold">الوصف</th>
                <th className="border border-gray-300 p-3 text-center font-semibold">الكمية</th>
                <th className="border border-gray-300 p-3 text-center font-semibold">السعر</th>
                <th className="border border-gray-300 p-3 text-center font-semibold">الإجمالي</th>
              </tr>
            </thead>
            <tbody>
              {invoice.items.map((item, index) => (
                <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="border border-gray-300 p-3">{item.description}</td>
                  <td className="border border-gray-300 p-3 text-center">{item.quantity}</td>
                  <td className="border border-gray-300 p-3 text-center">{formatCurrency(item.unitPrice)}</td>
                  <td className="border border-gray-300 p-3 text-center font-semibold">{formatCurrency(item.total)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Totals */}
        <div className="flex justify-end mb-8">
          <div className="w-80">
            <div className="space-y-2">
              <div className="flex justify-between py-2 border-b border-gray-200">
                <span className="text-gray-600">المجموع الفرعي:</span>
                <span className="font-semibold">{formatCurrency(invoice.subtotal)}</span>
              </div>
              {invoice.discountAmount > 0 && (
                <div className="flex justify-between py-2 border-b border-gray-200">
                  <span className="text-gray-600">الخصم:</span>
                  <span className="font-semibold text-red-600">-{formatCurrency(invoice.discountAmount)}</span>
                </div>
              )}
              {invoice.taxAmount > 0 && (
                <div className="flex justify-between py-2 border-b border-gray-200">
                  <span className="text-gray-600">الضريبة:</span>
                  <span className="font-semibold">{formatCurrency(invoice.taxAmount)}</span>
                </div>
              )}
              <div className="flex justify-between py-3 border-t-2 border-gray-400">
                <span className="text-lg font-bold text-gray-800">الإجمالي النهائي:</span>
                <span className="text-lg font-bold text-blue-600">{formatCurrency(invoice.total)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Notes */}
        {invoice.notes && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">ملاحظات:</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-gray-700">{invoice.notes}</p>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="border-t-2 border-gray-300 pt-6">
          <div className="text-center text-gray-600">
            <p className="mb-2">شكراً لك على ثقتك في خدماتنا</p>
            <p className="text-sm">هذه فاتورة مُنشأة إلكترونياً ولا تحتاج إلى توقيع</p>
          </div>
        </div>

        {/* Print Styles */}
        <style jsx>{`
          @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
            .print-break { page-break-before: always; }
          }
        `}</style>
      </div>
    )
  }
)

InvoicePrint.displayName = 'InvoicePrint'

export default InvoicePrint
