'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Scan, X, Camera, AlertCircle } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

interface BarcodeScannerProps {
  onScan: (barcode: string) => void
  isOpen: boolean
  onClose: () => void
}

export default function BarcodeScanner({ onScan, isOpen, onClose }: BarcodeScannerProps) {
  const [isScanning, setIsScanning] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  // تنظيف الكاميرا عند إغلاق المكون
  useEffect(() => {
    return () => {
      stopCamera()
    }
  }, [])

  // بدء الكاميرا
  const startCamera = async () => {
    try {
      setError(null)
      setIsScanning(true)

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // الكاميرا الخلفية
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      })

      streamRef.current = stream

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()

        // محاولة استخدام QuaggaJS للقراءة الحقيقية
        try {
          await initQuaggaScanner()
        } catch (quaggaError) {
          console.log('QuaggaJS غير متاح، استخدام المحاكاة')
          simulateBarcodeDetection()
        }
      }

    } catch (err) {
      console.error('خطأ في الوصول للكاميرا:', err)
      setError('لا يمكن الوصول للكاميرا. تأكد من منح الإذن للتطبيق.')
      setIsScanning(false)
    }
  }

  // تهيئة QuaggaJS للقراءة الحقيقية
  const initQuaggaScanner = async () => {
    try {
      const QuaggaModule = await import('quagga')
      const Quagga = QuaggaModule.default || QuaggaModule

      Quagga.init({
        inputStream: {
          name: "Live",
          type: "LiveStream",
          target: videoRef.current,
          constraints: {
            width: 640,
            height: 480,
            facingMode: "environment"
          }
        },
        decoder: {
          readers: [
            "code_128_reader",
            "ean_reader",
            "ean_8_reader",
            "code_39_reader",
            "code_39_vin_reader",
            "codabar_reader",
            "upc_reader",
            "upc_e_reader",
            "i2of5_reader"
          ]
        }
      }, (err: any) => {
        if (err) {
          console.error('خطأ في تهيئة QuaggaJS:', err)
          simulateBarcodeDetection()
          return
        }

        Quagga.start()

        Quagga.onDetected((data: any) => {
          const barcode = data.codeResult.code
          handleBarcodeDetected(barcode)
          Quagga.stop()
        })
      })
    } catch (error) {
      console.error('QuaggaJS غير متاح:', error)
      simulateBarcodeDetection()
    }
  }

  // إيقاف الكاميرا
  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    setIsScanning(false)
  }

  // محاكاة اكتشاف الباركود
  const simulateBarcodeDetection = () => {
    // محاكاة للعرض
    setTimeout(() => {
      const mockBarcodes = [
        '1234567890123',
        '9876543210987',
        '1111222233334',
        '5555666677778'
      ]

      const randomBarcode = mockBarcodes[Math.floor(Math.random() * mockBarcodes.length)]
      handleBarcodeDetected(randomBarcode)
    }, 3000) // محاكاة 3 ثواني للكشف
  }

  // معالجة اكتشاف الباركود
  const handleBarcodeDetected = (barcode: string) => {
    stopCamera()
    onScan(barcode)
    onClose()
    
    toast({
      title: "تم قراءة الباركود",
      description: `الباركود: ${barcode}`
    })
  }

  // إدخال الباركود يدوياً
  const handleManualInput = () => {
    const barcode = prompt('أدخل الباركود يدوياً:')
    if (barcode && barcode.trim()) {
      handleBarcodeDetected(barcode.trim())
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Scan className="h-5 w-5" />
            قارئ الباركود
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {error ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={startCamera} variant="outline">
                <Camera className="h-4 w-4 ml-2" />
                إعادة المحاولة
              </Button>
            </div>
          ) : isScanning ? (
            <div className="space-y-4">
              <div className="relative bg-black rounded-lg overflow-hidden">
                <video
                  ref={videoRef}
                  className="w-full h-64 object-cover"
                  autoPlay
                  playsInline
                  muted
                />
                
                {/* إطار المسح */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-48 h-32 border-2 border-white border-dashed rounded-lg flex items-center justify-center">
                    <div className="w-40 h-24 border-2 border-blue-500 rounded animate-pulse" />
                  </div>
                </div>

                {/* زر الإغلاق */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2 text-white hover:bg-white/20"
                  onClick={stopCamera}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">
                  وجه الكاميرا نحو الباركود
                </p>
                <div className="flex items-center justify-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Scan className="h-16 w-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">قراءة الباركود</h3>
              <p className="text-gray-600 mb-6">
                اختر طريقة قراءة الباركود
              </p>
              
              <div className="space-y-3">
                <Button onClick={startCamera} className="w-full">
                  <Camera className="h-4 w-4 ml-2" />
                  استخدام الكاميرا
                </Button>
                
                <Button onClick={handleManualInput} variant="outline" className="w-full">
                  إدخال يدوي
                </Button>
              </div>
            </div>
          )}

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              إلغاء
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// مكون زر قارئ الباركود
interface BarcodeScanButtonProps {
  onScan: (barcode: string) => void
  className?: string
}

export function BarcodeScanButton({ onScan, className }: BarcodeScanButtonProps) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Button
        variant="outline"
        onClick={() => setIsOpen(true)}
        className={className}
      >
        <Scan className="h-4 w-4 ml-2" />
        مسح الباركود
      </Button>
      
      <BarcodeScanner
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onScan={onScan}
      />
    </>
  )
}
