import { z } from 'zod'
import { createTRPCRouter, publicProcedure } from '../init'
import { db } from '@/lib/db'
import { sales, saleItems, products } from '@/lib/db/sqlite-schema'
import { eq } from 'drizzle-orm'

const saleItemSchema = z.object({
  productId: z.string(),
  productName: z.string(),
  quantity: z.number().min(0.01, 'الكمية يجب أن تكون أكبر من صفر'),
  unitPrice: z.number().min(0, 'السعر يجب أن يكون أكبر من صفر'),
  total: z.number().min(0),
})

const saleSchema = z.object({
  customerId: z.string().optional(),
  customerName: z.string().min(1, 'اسم العميل مطلوب'),
  items: z.array(saleItemSchema).min(1, 'يجب إضافة منتج واحد على الأقل'),
  subtotal: z.number().min(0),
  discount: z.number().min(0).default(0),
  tax: z.number().min(0).default(0),
  total: z.number().min(0),
  paymentType: z.enum(['cash', 'credit', 'partial']),
  paidAmount: z.number().min(0),
  remainingAmount: z.number().min(0).default(0),
  status: z.enum(['completed', 'pending', 'cancelled']).default('completed'),
  notes: z.string().optional(),
})

export const salesRouter = createTRPCRouter({
  // جلب جميع المبيعات
  getAll: publicProcedure.query(async () => {
    try {
      const allSales = await db.select().from(sales)
      
      // جلب العناصر لكل مبيعة
      const salesWithItems = await Promise.all(
        allSales.map(async (sale) => {
          const items = await db.select().from(saleItems).where(eq(saleItems.saleId, sale.id))
          return { ...sale, items }
        })
      )
      
      return salesWithItems
    } catch (error) {
      console.error('Error fetching sales:', error)
      return []
    }
  }),

  // جلب مبيعة واحدة
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      try {
        const saleResult = await db.select().from(sales).where(eq(sales.id, input.id))
        if (!saleResult[0]) return null
        
        const items = await db.select().from(saleItems).where(eq(saleItems.saleId, input.id))
        return { ...saleResult[0], items }
      } catch (error) {
        console.error('Error fetching sale:', error)
        return null
      }
    }),

  // إضافة مبيعة جديدة
  create: publicProcedure
    .input(saleSchema)
    .mutation(async ({ input }) => {
      try {
        const saleId = Date.now().toString(36) + Math.random().toString(36).substr(2)
        
        // إنشاء المبيعة
        const newSale = {
          id: saleId,
          customerId: input.customerId || null,
          customerName: input.customerName,
          subtotal: input.subtotal,
          discount: input.discount,
          tax: input.tax,
          total: input.total,
          paymentType: input.paymentType,
          paidAmount: input.paidAmount,
          remainingAmount: input.remainingAmount,
          status: input.status,
          notes: input.notes || null,
          companyId: 'default-company',
          createdAt: Date.now(),
          updatedAt: Date.now(),
        }
        
        await db.insert(sales).values(newSale)
        
        // إضافة عناصر المبيعة
        const saleItemsData = input.items.map(item => ({
          id: Date.now().toString(36) + Math.random().toString(36).substr(2),
          saleId,
          productId: item.productId,
          productName: item.productName,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total,
          createdAt: Date.now(),
        }))
        
        await db.insert(saleItems).values(saleItemsData)
        
        // تحديث المخزون
        for (const item of input.items) {
          await db.update(products)
            .set({ 
              stock: db.select({ stock: products.stock }).from(products).where(eq(products.id, item.productId)).then(result => 
                result[0] ? result[0].stock - item.quantity : 0
              )
            })
            .where(eq(products.id, item.productId))
        }
        
        return { ...newSale, items: saleItemsData }
      } catch (error) {
        console.error('Error creating sale:', error)
        throw new Error('فشل في إنشاء المبيعة')
      }
    }),

  // تحديث حالة المبيعة
  updateStatus: publicProcedure
    .input(z.object({
      id: z.string(),
      status: z.enum(['completed', 'pending', 'cancelled'])
    }))
    .mutation(async ({ input }) => {
      try {
        await db.update(sales)
          .set({ 
            status: input.status,
            updatedAt: Date.now()
          })
          .where(eq(sales.id, input.id))
        
        return { success: true }
      } catch (error) {
        console.error('Error updating sale status:', error)
        throw new Error('فشل في تحديث حالة المبيعة')
      }
    }),

  // إحصائيات المبيعات
  getStats: publicProcedure.query(async () => {
    try {
      const allSales = await db.select().from(sales)
      
      const today = new Date()
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime()
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1).getTime()
      
      const todaySales = allSales.filter(sale => sale.createdAt >= startOfDay)
      const monthSales = allSales.filter(sale => sale.createdAt >= startOfMonth)
      
      return {
        totalSales: allSales.length,
        completedSales: allSales.filter(s => s.status === 'completed').length,
        pendingSales: allSales.filter(s => s.status === 'pending').length,
        cancelledSales: allSales.filter(s => s.status === 'cancelled').length,
        totalRevenue: allSales.reduce((sum, sale) => sum + sale.total, 0),
        todayRevenue: todaySales.reduce((sum, sale) => sum + sale.total, 0),
        monthRevenue: monthSales.reduce((sum, sale) => sum + sale.total, 0),
        averageOrderValue: allSales.length > 0 ? allSales.reduce((sum, sale) => sum + sale.total, 0) / allSales.length : 0,
      }
    } catch (error) {
      console.error('Error fetching sales stats:', error)
      return {
        totalSales: 0,
        completedSales: 0,
        pendingSales: 0,
        cancelledSales: 0,
        totalRevenue: 0,
        todayRevenue: 0,
        monthRevenue: 0,
        averageOrderValue: 0,
      }
    }
  }),

  // البحث في المبيعات
  search: publicProcedure
    .input(z.object({
      query: z.string(),
      status: z.enum(['completed', 'pending', 'cancelled']).optional(),
      paymentType: z.enum(['cash', 'credit', 'partial']).optional(),
    }))
    .query(async ({ input }) => {
      try {
        let allSales = await db.select().from(sales)
        
        // تطبيق الفلاتر
        if (input.status) {
          allSales = allSales.filter(sale => sale.status === input.status)
        }
        
        if (input.paymentType) {
          allSales = allSales.filter(sale => sale.paymentType === input.paymentType)
        }
        
        // البحث النصي
        if (input.query) {
          allSales = allSales.filter(sale => 
            sale.customerName.toLowerCase().includes(input.query.toLowerCase()) ||
            sale.id.includes(input.query)
          )
        }
        
        // جلب العناصر
        const salesWithItems = await Promise.all(
          allSales.map(async (sale) => {
            const items = await db.select().from(saleItems).where(eq(saleItems.saleId, sale.id))
            return { ...sale, items }
          })
        )
        
        return salesWithItems
      } catch (error) {
        console.error('Error searching sales:', error)
        return []
      }
    }),
})
