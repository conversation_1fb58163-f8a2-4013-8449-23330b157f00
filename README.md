# نظام المحاسبة الحديث 🧮

نظام محاسبة متطور وحديث مبني بأحدث التقنيات لإدارة الأعمال والمحاسبة.

## 🚀 التقنيات المستخدمة

### Frontend
- **Next.js 15** - إطار عمل React الحديث
- **TypeScript** - للكتابة الآمنة
- **Tailwind CSS** - للتصميم السريع والمرن
- **Shadcn/ui** - مكتبة مكونات UI حديثة

### Backend
- **tRPC** - للـ APIs المكتوبة بـ TypeScript
- **Drizzle ORM** - ORM حديث وسريع
- **PostgreSQL** - قاعدة بيانات قوية ومتقدمة

### المصادقة والأمان
- **Clerk** - نظام مصادقة متقدم
- **Zod** - للتحقق من صحة البيانات

## ✨ الميزات

### 📊 لوحة التحكم
- عرض شامل للإحصائيات المالية
- مخططات بيانية تفاعلية للإيرادات والمصروفات
- مؤشرات الأداء الرئيسية (KPIs)
- الإجراءات السريعة والاختصارات

### 📄 إدارة الفواتير
- إنشاء وتعديل الفواتير بسهولة
- إدارة العناصر والخدمات ديناميكياً
- حساب الضرائب والخصومات تلقائياً
- تتبع حالة الفواتير (مسودة، مرسلة، مدفوعة، متأخرة)

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع تاريخ المعاملات
- إحصائيات العملاء والمبيعات

### 📦 إدارة المنتجات والخدمات
- كتالوج شامل للمنتجات والخدمات
- إدارة المخزون والكميات
- تصنيف المنتجات والخدمات

### 💳 إدارة المدفوعات
- تسجيل ومتابعة جميع المدفوعات
- دعم طرق دفع متعددة (تحويل بنكي، بطاقة ائتمان، نقداً، شيك)
- تتبع حالة المدفوعات
- ربط المدفوعات بالفواتير

### 💰 إدارة المصروفات
- تسجيل وتصنيف المصروفات
- تتبع المصروفات حسب الفئات
- إدارة الموردين والفواتير
- تقارير المصروفات التفصيلية

### 📈 التقارير والتحليلات
- تقارير مالية شاملة
- تحليل الإيرادات والمصروفات
- تقارير العملاء والمنتجات

### ⚙️ الإعدادات
- إعدادات الشركة والمعلومات الأساسية
- إعدادات الفواتير والضرائب
- إعدادات الإشعارات
- إدارة المستخدمين

### 🔧 مميزات تقنية
- 📱 **تصميم متجاوب** - يعمل على جميع الأجهزة
- 🌙 **الوضع المظلم** - دعم للوضع المظلم
- 🔒 **أمان متقدم** - حماية البيانات والخصوصية
- 🌐 **دعم RTL** - دعم كامل للغة العربية
- ⚡ **أداء عالي** - تحميل سريع وتفاعل سلس

## 🛠️ التثبيت والإعداد

### المتطلبات
- Node.js 18+ 
- PostgreSQL
- حساب Clerk للمصادقة

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd modern-accounting-system
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env.local
```
ثم قم بتعديل الملف `.env.local` وإضافة:
- رابط قاعدة البيانات
- مفاتيح Clerk

4. **إعداد قاعدة البيانات**
```bash
npm run db:generate
npm run db:migrate
```

5. **تشغيل المشروع**
```bash
npm run dev
```

المشروع سيعمل على: `http://localhost:3000`

## 📁 هيكل المشروع

```
src/
├── app/                 # صفحات Next.js
├── components/          # مكونات UI
│   └── ui/             # مكونات Shadcn/ui
├── lib/                # مكتبات ووظائف مساعدة
│   ├── db/             # إعداد قاعدة البيانات
│   └── trpc/           # إعداد tRPC
├── hooks/              # React Hooks مخصصة
└── types/              # تعريفات TypeScript
```

## 🗄️ قاعدة البيانات

النظام يستخدم PostgreSQL مع الجداول التالية:
- `companies` - بيانات الشركات
- `customers` - بيانات العملاء  
- `products` - المنتجات والخدمات
- `invoices` - الفواتير
- `invoice_items` - عناصر الفاتورة
- `payments` - المدفوعات
- `expenses` - المصروفات

## 🔧 الأوامر المتاحة

```bash
npm run dev          # تشغيل المشروع في وضع التطوير
npm run build        # بناء المشروع للإنتاج
npm run start        # تشغيل المشروع المبني
npm run lint         # فحص الكود
npm run db:generate  # إنشاء ملفات قاعدة البيانات
npm run db:migrate   # تطبيق التغييرات على قاعدة البيانات
npm run db:studio    # فتح واجهة إدارة قاعدة البيانات
```

## 🚀 النشر

يمكن نشر المشروع على:
- **Vercel** (موصى به للـ Frontend)
- **Railway** أو **Fly.io** (للـ Backend)
- **Supabase** (لقاعدة البيانات)

## 📱 التطوير للهاتف

المشروع مصمم ليكون متجاوباً ويمكن تطويره لاحقاً إلى:
- تطبيق React Native
- PWA (Progressive Web App)
- تطبيق Capacitor

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. عمل commit للتغييرات
4. إرسال Pull Request

## 📄 الرخصة

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للدعم والاستفسارات، يرجى فتح issue في GitHub.

---

**تم تطويره بـ ❤️ باستخدام أحدث التقنيات**
