'use client'

import React, { useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Printer, Download, FileText, Share } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

interface PrintManagerProps {
  children: React.ReactNode
  filename?: string
  title?: string
}

export default function PrintManager({ children, filename = 'document', title = 'طباعة المستند' }: PrintManagerProps) {
  const printRef = useRef<HTMLDivElement>(null)

  // طباعة المستند
  const handlePrint = () => {
    if (!printRef.current) return

    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      toast({
        title: "خطأ",
        description: "لا يمكن فتح نافذة الطباعة. تأكد من السماح للنوافذ المنبثقة.",
        variant: "destructive"
      })
      return
    }

    const printContent = printRef.current.innerHTML
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <title>${title}</title>
        <meta charset="utf-8">
        <style>
          body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            direction: rtl;
            background: white;
          }
          .no-print { display: none !important; }
          table { border-collapse: collapse; width: 100%; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .text-center { text-align: center; }
          .text-left { text-align: left; }
          .font-bold { font-weight: bold; }
          .text-blue-600 { color: #2563eb; }
          .text-red-600 { color: #dc2626; }
          .text-green-600 { color: #16a34a; }
          .text-gray-600 { color: #4b5563; }
          .text-gray-800 { color: #1f2937; }
          .bg-gray-50 { background-color: #f9fafb; }
          .bg-gray-100 { background-color: #f3f4f6; }
          .border { border: 1px solid #d1d5db; }
          .border-b { border-bottom: 1px solid #d1d5db; }
          .border-t-2 { border-top: 2px solid #d1d5db; }
          .border-b-2 { border-bottom: 2px solid #d1d5db; }
          .border-gray-300 { border-color: #d1d5db; }
          .border-gray-400 { border-color: #9ca3af; }
          .p-3 { padding: 12px; }
          .p-4 { padding: 16px; }
          .p-6 { padding: 24px; }
          .p-8 { padding: 32px; }
          .mb-2 { margin-bottom: 8px; }
          .mb-3 { margin-bottom: 12px; }
          .mb-6 { margin-bottom: 24px; }
          .mb-8 { margin-bottom: 32px; }
          .pb-6 { padding-bottom: 24px; }
          .pt-6 { padding-top: 24px; }
          .py-2 { padding-top: 8px; padding-bottom: 8px; }
          .py-3 { padding-top: 12px; padding-bottom: 12px; }
          .space-y-1 > * + * { margin-top: 4px; }
          .space-y-2 > * + * { margin-top: 8px; }
          .rounded-lg { border-radius: 8px; }
          .text-sm { font-size: 14px; }
          .text-lg { font-size: 18px; }
          .text-xl { font-size: 20px; }
          .text-2xl { font-size: 24px; }
          .text-3xl { font-size: 30px; }
          .font-semibold { font-weight: 600; }
          .w-80 { width: 320px; }
          .max-w-4xl { max-width: 896px; }
          .mx-auto { margin-left: auto; margin-right: auto; }
          .flex { display: flex; }
          .justify-between { justify-content: space-between; }
          .justify-end { justify-content: flex-end; }
          .items-start { align-items: flex-start; }
          .items-center { align-items: center; }
          @media print {
            body { margin: 0; padding: 0; }
            .no-print { display: none !important; }
            .print-break { page-break-before: always; }
          }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `)
    
    printWindow.document.close()
    printWindow.focus()
    
    setTimeout(() => {
      printWindow.print()
      printWindow.close()
    }, 250)

    toast({
      title: "تم بنجاح",
      description: "تم إرسال المستند للطباعة"
    })
  }

  // تصدير كـ PDF حقيقي
  const handleExportPDF = async () => {
    try {
      if (!printRef.current) {
        toast({
          title: "خطأ",
          description: "لا يوجد محتوى للتصدير",
          variant: "destructive"
        })
        return
      }

      toast({
        title: "جاري التصدير...",
        description: "يرجى الانتظار أثناء إنشاء ملف PDF"
      })

      // استيراد المكتبات ديناميكياً
      const [{ jsPDF }, html2canvas] = await Promise.all([
        import('jspdf'),
        import('html2canvas')
      ])

      // تحويل HTML إلى صورة
      const canvas = await html2canvas.default(printRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
        width: printRef.current.scrollWidth,
        height: printRef.current.scrollHeight
      })

      const imgData = canvas.toDataURL('image/png')

      // إنشاء PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      })

      const imgWidth = 210 // عرض A4
      const pageHeight = 295 // ارتفاع A4
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight
      let position = 0

      // إضافة الصفحة الأولى
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      // إضافة صفحات إضافية إذا لزم الأمر
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      // حفظ الملف
      pdf.save(`${filename}.pdf`)

      toast({
        title: "تم بنجاح",
        description: "تم تصدير ملف PDF بنجاح"
      })

    } catch (error) {
      console.error('خطأ في تصدير PDF:', error)

      // fallback للطباعة العادية
      toast({
        title: "تحذير",
        description: "فشل تصدير PDF، سيتم استخدام الطباعة العادية",
        variant: "destructive"
      })
      handlePrint()
    }
  }

  // مشاركة المستند
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: `مشاركة ${title}`,
          url: window.location.href
        })
      } catch (error) {
        // المستخدم ألغى المشاركة
      }
    } else {
      // نسخ الرابط للحافظة
      try {
        await navigator.clipboard.writeText(window.location.href)
        toast({
          title: "تم بنجاح",
          description: "تم نسخ الرابط إلى الحافظة"
        })
      } catch (error) {
        toast({
          title: "خطأ",
          description: "لا يمكن نسخ الرابط",
          variant: "destructive"
        })
      }
    }
  }

  return (
    <div>
      {/* أزرار التحكم */}
      <div className="flex gap-2 mb-4 no-print">
        <Button onClick={handlePrint} className="flex items-center gap-2">
          <Printer className="h-4 w-4" />
          طباعة
        </Button>
        
        <Button onClick={handleExportPDF} variant="outline" className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          تصدير PDF
        </Button>
        
        <Button onClick={handleShare} variant="outline" className="flex items-center gap-2">
          <Share className="h-4 w-4" />
          مشاركة
        </Button>
      </div>

      {/* المحتوى القابل للطباعة */}
      <div ref={printRef}>
        {children}
      </div>
    </div>
  )
}
