import { realDataManager } from '@/lib/real-storage'

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

describe('realDataManager', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  describe('getProducts', () => {
    test('should return empty array when no products in storage', () => {
      const products = realDataManager.getProducts()
      expect(products).toEqual([])
    })

    test('should return products from localStorage', () => {
      const mockProducts = [
        {
          id: '1',
          name: 'حديد تسليح',
          category: 'حديد',
          price: 850,
          cost: 750,
          stock: 100,
          minStock: 10,
          unit: 'طن',
          supplier: 'مورد',
          location: 'مخزن',
          description: 'وصف',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ]
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockProducts))
      
      const products = realDataManager.getProducts()
      expect(products).toEqual(mockProducts)
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('products')
    })

    test('should handle JSON parse errors gracefully', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid json')
      
      const products = realDataManager.getProducts()
      expect(products).toEqual([])
    })
  })

  describe('getCustomers', () => {
    test('should return empty array when no customers in storage', () => {
      const customers = realDataManager.getCustomers()
      expect(customers).toEqual([])
    })

    test('should return customers from localStorage', () => {
      const mockCustomers = [
        {
          id: '1',
          name: 'أحمد محمد',
          phone: '771234567',
          type: 'individual',
          status: 'active',
          creditLimit: 10000,
          currentDebt: 0,
          totalPurchases: 5000,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ]
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockCustomers))
      
      const customers = realDataManager.getCustomers()
      expect(customers).toEqual(mockCustomers)
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('customers')
    })
  })

  describe('getSales', () => {
    test('should return empty array when no sales in storage', () => {
      const sales = realDataManager.getSales()
      expect(sales).toEqual([])
    })

    test('should return sales from localStorage', () => {
      const mockSales = [
        {
          id: '1',
          customerName: 'عميل تجريبي',
          total: 1000,
          status: 'completed',
          paymentType: 'cash',
          items: [],
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ]
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockSales))
      
      const sales = realDataManager.getSales()
      expect(sales).toEqual(mockSales)
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('sales')
    })
  })

  describe('initializeData', () => {
    test('should initialize data when localStorage is empty', () => {
      realDataManager.initializeData()
      
      // Should call setItem for each data type
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('products', expect.any(String))
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('customers', expect.any(String))
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('sales', expect.any(String))
    })

    test('should not overwrite existing data', () => {
      mockLocalStorage.getItem.mockReturnValue('[]')
      
      realDataManager.initializeData()
      
      // Should not call setItem when data already exists
      expect(mockLocalStorage.setItem).not.toHaveBeenCalled()
    })
  })
})
