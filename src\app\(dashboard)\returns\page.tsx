'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  ArrowLeft, Search, RefreshCw, AlertTriangle, CheckCircle,
  XCircle, Calendar, User, Phone, Receipt, Package,
  Minus, Plus, Save, Trash2, Eye, FileText, Download
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatCurrency } from '@/lib/utils'
import { realDataManager } from '@/lib/real-storage'

export default function ReturnsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTab, setSelectedTab] = useState<'new' | 'history'>('new')
  const [salesHistory, setSalesHistory] = useState<any[]>([])
  const [returnsHistory, setReturnsHistory] = useState<any[]>([])
  const [selectedSale, setSelectedSale] = useState<any>(null)
  const [returnItems, setReturnItems] = useState<any[]>([])
  const [returnReason, setReturnReason] = useState('')
  const [showReturnModal, setShowReturnModal] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    loadData()
  }, [])

  const loadData = () => {
    const sales = realDataManager.getSales()
    const returns = realDataManager.getReturns()

    setSalesHistory(sales)
    setReturnsHistory(returns)
  }

  if (!mounted) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري التحميل...</p>
        </div>
      </div>
    )
  }

  // إحصائيات المرتجعات
  const totalReturns = returnsHistory.length
  const totalReturnValue = returnsHistory.reduce((sum, ret) => sum + ret.totalAmount, 0)
  const todayReturns = returnsHistory.filter(ret => {
    const today = new Date().toDateString()
    const returnDate = new Date(ret.createdAt).toDateString()
    return today === returnDate
  }).length

  // تصفية المبيعات للبحث
  const filteredSales = salesHistory.filter(sale =>
    sale.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    sale.id.includes(searchTerm)
  )

  // تصفية المرتجعات للبحث
  const filteredReturns = returnsHistory.filter(ret =>
    ret.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ret.id.includes(searchTerm)
  )

  const processReturn = () => {
    if (!selectedSale || returnItems.length === 0) return

    const returnData = {
      saleId: selectedSale.id,
      customerName: selectedSale.customerName,
      items: returnItems,
      reason: returnReason,
      totalAmount: returnItems.reduce((sum, item) => sum + (item.unitPrice * item.returnQuantity), 0)
    }

    const newReturn = realDataManager.addReturn(returnData)

    // تحديث البيانات
    loadData()

    // إعادة تعيين النموذج
    setSelectedSale(null)
    setReturnItems([])
    setReturnReason('')
    setShowReturnModal(false)
  }

  const selectSaleForReturn = (sale: any) => {
    setSelectedSale(sale)
    setReturnItems(sale.items.map((item: any) => ({
      ...item,
      returnQuantity: 0,
      maxQuantity: item.quantity
    })))
    setShowReturnModal(true)
  }

  const updateReturnQuantity = (itemId: string, quantity: number) => {
    setReturnItems(prev => prev.map(item =>
      item.id === itemId
        ? { ...item, returnQuantity: Math.max(0, Math.min(quantity, item.maxQuantity)) }
        : item
    ))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col" dir="rtl">
      {/* Header */}
      <div className="bg-white shadow-sm border-b p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">إدارة المرتجعات</h1>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={loadData}>
              <RefreshCw className="h-4 w-4 ml-2" />
              تحديث
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 ml-2" />
              تصدير
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="bg-white border-b p-4 flex-shrink-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي المرتجعات</p>
                  <p className="text-2xl font-bold text-blue-600">{totalReturns}</p>
                </div>
                <RefreshCw className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">قيمة المرتجعات</p>
                  <p className="text-2xl font-bold text-red-600">{totalReturnValue.toLocaleString()} ر.ي</p>
                </div>
                <Package className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">مرتجعات اليوم</p>
                  <p className="text-2xl font-bold text-yellow-600">{todayReturns}</p>
                </div>
                <Calendar className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b p-4 flex-shrink-0">
        <div className="flex gap-4">
          <Button
            variant={selectedTab === 'new' ? 'default' : 'outline'}
            onClick={() => setSelectedTab('new')}
          >
            مرتجع جديد
          </Button>
          <Button
            variant={selectedTab === 'history' ? 'default' : 'outline'}
            onClick={() => setSelectedTab('history')}
          >
            سجل المرتجعات
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white border-b p-4 flex-shrink-0">
        <div className="relative max-w-md">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder={selectedTab === 'new' ? "البحث في المبيعات..." : "البحث في المرتجعات..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {selectedTab === 'new' ? (
          <Card>
            <CardHeader>
              <CardTitle>اختر فاتورة للمرتجع ({filteredSales.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredSales.length === 0 ? (
                <p className="text-center text-gray-500 py-8">لا توجد مبيعات</p>
              ) : (
                <div className="space-y-4">
                  {filteredSales.map((sale) => (
                    <div key={sale.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-4 mb-2">
                            <h3 className="font-semibold">فاتورة #{sale.id}</h3>
                            <Badge className={getStatusColor(sale.status)}>
                              {sale.status === 'completed' ? 'مكتملة' : sale.status === 'pending' ? 'معلقة' : 'ملغية'}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              {sale.customerName}
                            </div>
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              {new Date(sale.createdAt).toLocaleDateString('ar-SA')}
                            </div>
                            <div className="flex items-center gap-2">
                              <Receipt className="h-4 w-4" />
                              {sale.total.toLocaleString()} ر.ي
                            </div>
                            <div className="flex items-center gap-2">
                              <Package className="h-4 w-4" />
                              {sale.items.length} منتج
                            </div>
                          </div>
                        </div>
                        <Button
                          onClick={() => selectSaleForReturn(sale)}
                          disabled={sale.status !== 'completed'}
                        >
                          إنشاء مرتجع
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>سجل المرتجعات ({filteredReturns.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredReturns.length === 0 ? (
                <p className="text-center text-gray-500 py-8">لا توجد مرتجعات</p>
              ) : (
                <div className="space-y-4">
                  {filteredReturns.map((returnItem) => (
                    <div key={returnItem.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold">مرتجع #{returnItem.id}</h3>
                        <Badge className="bg-blue-100 text-blue-800">
                          {returnItem.status || 'مكتمل'}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          {returnItem.customerName}
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          {new Date(returnItem.createdAt).toLocaleDateString('ar-SA')}
                        </div>
                        <div className="flex items-center gap-2">
                          <Receipt className="h-4 w-4" />
                          {returnItem.totalAmount.toLocaleString()} ر.ي
                        </div>
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4" />
                          {returnItem.items.length} منتج
                        </div>
                      </div>
                      {returnItem.reason && (
                        <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                          <strong>السبب:</strong> {returnItem.reason}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Return Modal */}
      {showReturnModal && selectedSale && (
        <div className="fixed inset-0 z-50">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowReturnModal(false)} />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">إنشاء مرتجع - فاتورة #{selectedSale.id}</h2>
                <Button variant="ghost" size="sm" onClick={() => setShowReturnModal(false)}>
                  <XCircle className="h-4 w-4" />
                </Button>
              </div>

              {/* Customer Info */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 className="font-semibold mb-2">معلومات العميل</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">الاسم:</span> {selectedSale.customerName}
                  </div>
                  <div>
                    <span className="text-gray-600">تاريخ الفاتورة:</span> {new Date(selectedSale.createdAt).toLocaleDateString('ar-SA')}
                  </div>
                </div>
              </div>

              {/* Return Items */}
              <div className="mb-6">
                <h3 className="font-semibold mb-4">المنتجات المراد إرجاعها</h3>
                <div className="space-y-3">
                  {returnItems.map((item) => (
                    <div key={item.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium">{item.productName}</h4>
                          <p className="text-sm text-gray-600">
                            السعر: {item.unitPrice.toLocaleString()} ر.ي | الكمية المباعة: {item.quantity}
                          </p>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="text-sm text-gray-600">كمية الإرجاع:</span>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateReturnQuantity(item.id, item.returnQuantity - 1)}
                              disabled={item.returnQuantity <= 0}
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                            <span className="w-12 text-center">{item.returnQuantity}</span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateReturnQuantity(item.id, item.returnQuantity + 1)}
                              disabled={item.returnQuantity >= item.maxQuantity}
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                          <span className="text-sm font-medium">
                            {(item.unitPrice * item.returnQuantity).toLocaleString()} ر.ي
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Return Reason */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  سبب الإرجاع
                </label>
                <textarea
                  value={returnReason}
                  onChange={(e) => setReturnReason(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none h-20"
                  placeholder="اكتب سبب الإرجاع..."
                />
              </div>

              {/* Return Summary */}
              <div className="bg-blue-50 rounded-lg p-4 mb-6">
                <h3 className="font-semibold mb-2">ملخص المرتجع</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">عدد المنتجات:</span> {returnItems.filter(item => item.returnQuantity > 0).length}
                  </div>
                  <div>
                    <span className="text-gray-600">إجمالي المبلغ:</span> {returnItems.reduce((sum, item) => sum + (item.unitPrice * item.returnQuantity), 0).toLocaleString()} ر.ي
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-3">
                <Button variant="outline" onClick={() => setShowReturnModal(false)} className="flex-1">
                  إلغاء
                </Button>
                <Button
                  onClick={processReturn}
                  className="flex-1"
                  disabled={returnItems.filter(item => item.returnQuantity > 0).length === 0}
                >
                  <Save className="h-4 w-4 ml-2" />
                  تأكيد المرتجع
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
