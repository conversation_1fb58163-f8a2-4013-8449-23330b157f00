import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "../init";

const paymentSchema = z.object({
  id: z.string(),
  invoiceId: z.string(),
  customerName: z.string(),
  amount: z.number(),
  paymentDate: z.string(),
  paymentMethod: z.enum(['bank_transfer', 'credit_card', 'cash', 'check']),
  status: z.enum(['completed', 'pending', 'failed']),
  reference: z.string(),
  notes: z.string().optional(),
});

const createPaymentSchema = z.object({
  invoiceId: z.string(),
  amount: z.number().positive(),
  paymentMethod: z.enum(['bank_transfer', 'credit_card', 'cash', 'check']),
  reference: z.string(),
  notes: z.string().optional(),
});

const updatePaymentSchema = z.object({
  id: z.string(),
  status: z.enum(['completed', 'pending', 'failed']).optional(),
  notes: z.string().optional(),
});

// Mock data - في التطبيق الحقيقي، هذه البيانات ستأتي من قاعدة البيانات
const mockPayments = [
  {
    id: 'PAY-001',
    invoiceId: 'INV-001',
    customerName: 'شركة التقنية المتقدمة',
    amount: 15000,
    paymentDate: '2024-01-15',
    paymentMethod: 'bank_transfer' as const,
    status: 'completed' as const,
    reference: 'TXN-*********',
    notes: 'تم الدفع بالكامل'
  },
  {
    id: 'PAY-002',
    invoiceId: 'INV-002',
    customerName: 'مؤسسة الابتكار الرقمي',
    amount: 8500,
    paymentDate: '2024-01-14',
    paymentMethod: 'credit_card' as const,
    status: 'completed' as const,
    reference: 'CC-*********',
    notes: ''
  },
  {
    id: 'PAY-003',
    invoiceId: 'INV-003',
    customerName: 'شركة الحلول الذكية',
    amount: 12000,
    paymentDate: '2024-01-13',
    paymentMethod: 'cash' as const,
    status: 'pending' as const,
    reference: 'CASH-001',
    notes: 'في انتظار التأكيد'
  }
];

export const paymentsRouter = createTRPCRouter({
  // الحصول على جميع المدفوعات
  getAll: publicProcedure.query(async () => {
    // في التطبيق الحقيقي، سيتم جلب البيانات من قاعدة البيانات
    return mockPayments;
  }),

  // الحصول على مدفوعة واحدة بالمعرف
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const payment = mockPayments.find(p => p.id === input.id);
      if (!payment) {
        throw new Error('Payment not found');
      }
      return payment;
    }),

  // إنشاء مدفوعة جديدة
  create: publicProcedure
    .input(createPaymentSchema)
    .mutation(async ({ input }) => {
      // في التطبيق الحقيقي، سيتم حفظ البيانات في قاعدة البيانات
      const newPayment = {
        id: `PAY-${String(mockPayments.length + 1).padStart(3, '0')}`,
        customerName: 'عميل جديد', // سيتم جلب اسم العميل من الفاتورة
        paymentDate: new Date().toISOString().split('T')[0],
        status: 'pending' as const,
        ...input,
      };
      
      mockPayments.push(newPayment);
      return newPayment;
    }),

  // تحديث مدفوعة
  update: publicProcedure
    .input(updatePaymentSchema)
    .mutation(async ({ input }) => {
      const paymentIndex = mockPayments.findIndex(p => p.id === input.id);
      if (paymentIndex === -1) {
        throw new Error('Payment not found');
      }

      const updatedPayment = {
        ...mockPayments[paymentIndex],
        ...input,
      };

      mockPayments[paymentIndex] = updatedPayment;
      return updatedPayment;
    }),

  // حذف مدفوعة
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      const paymentIndex = mockPayments.findIndex(p => p.id === input.id);
      if (paymentIndex === -1) {
        throw new Error('Payment not found');
      }

      const deletedPayment = mockPayments.splice(paymentIndex, 1)[0];
      return deletedPayment;
    }),

  // الحصول على إحصائيات المدفوعات
  getStats: publicProcedure.query(async () => {
    const totalPayments = mockPayments.reduce((sum, payment) => sum + payment.amount, 0);
    const completedPayments = mockPayments
      .filter(p => p.status === 'completed')
      .reduce((sum, payment) => sum + payment.amount, 0);
    const pendingPayments = mockPayments
      .filter(p => p.status === 'pending')
      .reduce((sum, payment) => sum + payment.amount, 0);
    const failedPayments = mockPayments
      .filter(p => p.status === 'failed')
      .reduce((sum, payment) => sum + payment.amount, 0);

    return {
      total: totalPayments,
      completed: completedPayments,
      pending: pendingPayments,
      failed: failedPayments,
      count: mockPayments.length,
      completedCount: mockPayments.filter(p => p.status === 'completed').length,
      pendingCount: mockPayments.filter(p => p.status === 'pending').length,
      failedCount: mockPayments.filter(p => p.status === 'failed').length,
    };
  }),

  // البحث في المدفوعات
  search: publicProcedure
    .input(z.object({
      query: z.string().optional(),
      status: z.enum(['completed', 'pending', 'failed']).optional(),
      paymentMethod: z.enum(['bank_transfer', 'credit_card', 'cash', 'check']).optional(),
    }))
    .query(async ({ input }) => {
      let filteredPayments = mockPayments;

      if (input.query) {
        const query = input.query.toLowerCase();
        filteredPayments = filteredPayments.filter(payment =>
          payment.customerName.toLowerCase().includes(query) ||
          payment.invoiceId.toLowerCase().includes(query) ||
          payment.id.toLowerCase().includes(query) ||
          payment.reference.toLowerCase().includes(query)
        );
      }

      if (input.status) {
        filteredPayments = filteredPayments.filter(payment => payment.status === input.status);
      }

      if (input.paymentMethod) {
        filteredPayments = filteredPayments.filter(payment => payment.paymentMethod === input.paymentMethod);
      }

      return filteredPayments;
    }),
});

export type PaymentsRouter = typeof paymentsRouter;
