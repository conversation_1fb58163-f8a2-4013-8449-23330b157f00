'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  LayoutDashboard,
  FileText,
  Users,
  Package,
  CreditCard,
  BarChart3,
  Settings,
  Menu,
  X,
  Receipt,
  LogOut,
  ShoppingCart,
  RotateCcw,
  Warehouse
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { User } from 'lucide-react'

const navigation = [
  {
    name: 'لوحة التحكم',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'نقطة البيع',
    href: '/pos',
    icon: ShoppingCart,
  },
  {
    name: 'المرتجعات',
    href: '/returns',
    icon: RotateCcw,
  },
  {
    name: 'إدارة المخزون',
    href: '/inventory',
    icon: Warehouse,
  },
  {
    name: 'الفواتير',
    href: '/invoices',
    icon: FileText,
  },
  {
    name: 'العملاء',
    href: '/customers',
    icon: Users,
  },
  {
    name: 'إدارة العملاء',
    href: '/customer-management',
    icon: Users,
  },
  {
    name: 'المنتجات',
    href: '/products',
    icon: Package,
  },
  {
    name: 'المدفوعات',
    href: '/payments',
    icon: CreditCard,
  },
  {
    name: 'المصروفات',
    href: '/expenses',
    icon: Receipt,
  },
  {
    name: 'التقارير',
    href: '/reports',
    icon: BarChart3,
  },
  {
    name: 'الإعدادات',
    href: '/settings',
    icon: Settings,
  },
]

interface SidebarProps {
  className?: string
}

export default function Sidebar({ className }: SidebarProps) {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()

  return (
    <>
      {/* Menu button - always visible */}
      <div className="fixed top-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="bg-white shadow-md hover:bg-gray-50"
        >
          {isOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
        </Button>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar - always overlay */}
      <div className={cn(
        'fixed inset-y-0 right-0 z-50 w-80 bg-white shadow-2xl transform transition-transform duration-300 ease-in-out',
        isOpen ? 'translate-x-0' : 'translate-x-full',
        className
      )}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 border-b bg-gradient-to-r from-blue-600 to-blue-700">
            <h1 className="text-xl font-bold text-white">نظام المحاسبة</h1>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsOpen(false)}
                  className={cn(
                    'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200',
                    isActive
                      ? 'bg-blue-100 text-blue-700 border-l-4 border-blue-700 shadow-sm'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'
                  )}
                >
                  <item.icon className="ml-3 h-5 w-5" />
                  {item.name}
                </Link>
              )
            })}
          </nav>

          {/* User section */}
          <div className="border-t bg-gray-50 px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div className="text-sm">
                  <div className="font-medium text-gray-900">المستخدم</div>
                  <div className="text-gray-500"><EMAIL></div>
                </div>
              </div>
              <Button variant="ghost" size="sm" className="hover:bg-red-50 hover:text-red-600">
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
