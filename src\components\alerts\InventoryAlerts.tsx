'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Package, TrendingDown, X, Eye, RefreshCw } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
// import { realDataManager, Product } from '@/lib/real-storage'
import { toast } from '@/hooks/use-toast'

interface Product {
  id: string
  name: string
  stock: number
  minStock: number
  unit: string
}

interface InventoryAlert {
  id: string
  type: 'low_stock' | 'out_of_stock' | 'overstock'
  product: Product
  message: string
  severity: 'high' | 'medium' | 'low'
  timestamp: number
}

export default function InventoryAlerts() {
  const [alerts, setAlerts] = useState<InventoryAlert[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [showDismissed, setShowDismissed] = useState(false)
  const [dismissedAlerts, setDismissedAlerts] = useState<string[]>([])

  useEffect(() => {
    loadData()
    // تحديث التنبيهات كل دقيقة
    const interval = setInterval(loadData, 60000)
    return () => clearInterval(interval)
  }, [])

  const loadData = () => {
    // بيانات تجريبية بسيطة
    const productsData: Product[] = [
      { id: '1', name: 'حديد تسليح 12مم', stock: 5, minStock: 20, unit: 'طن' },
      { id: '2', name: 'أسمنت بورتلاند', stock: 0, minStock: 50, unit: 'كيس' },
      { id: '3', name: 'رمل مغسول', stock: 15, minStock: 30, unit: 'متر مكعب' }
    ]
    setProducts(productsData)
    generateAlerts(productsData)
  }

  const generateAlerts = (productsData: Product[]) => {
    const newAlerts: InventoryAlert[] = []

    productsData.forEach(product => {
      // تنبيه نفاد المخزون
      if (product.stock === 0) {
        newAlerts.push({
          id: `out_of_stock_${product.id}`,
          type: 'out_of_stock',
          product,
          message: `نفد مخزون ${product.name} تماماً`,
          severity: 'high',
          timestamp: Date.now()
        })
      }
      // تنبيه المخزون المنخفض
      else if (product.stock <= product.minStock) {
        newAlerts.push({
          id: `low_stock_${product.id}`,
          type: 'low_stock',
          product,
          message: `مخزون ${product.name} منخفض (${product.stock} متبقي)`,
          severity: 'medium',
          timestamp: Date.now()
        })
      }
      // تنبيه المخزون الزائد (إذا كان أكثر من 10 أضعاف الحد الأدنى)
      else if (product.stock > product.minStock * 10) {
        newAlerts.push({
          id: `overstock_${product.id}`,
          type: 'overstock',
          product,
          message: `مخزون ${product.name} زائد عن الحاجة (${product.stock} متوفر)`,
          severity: 'low',
          timestamp: Date.now()
        })
      }
    })

    // تصفية التنبيهات المرفوضة
    const filteredAlerts = newAlerts.filter(alert => !dismissedAlerts.includes(alert.id))
    setAlerts(filteredAlerts)
  }

  const dismissAlert = (alertId: string) => {
    setDismissedAlerts(prev => [...prev, alertId])
    setAlerts(prev => prev.filter(alert => alert.id !== alertId))
    
    toast({
      title: "تم إخفاء التنبيه",
      description: "تم إخفاء التنبيه بنجاح"
    })
  }

  const dismissAllAlerts = () => {
    const allAlertIds = alerts.map(alert => alert.id)
    setDismissedAlerts(prev => [...prev, ...allAlertIds])
    setAlerts([])
    
    toast({
      title: "تم إخفاء جميع التنبيهات",
      description: "تم إخفاء جميع التنبيهات بنجاح"
    })
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'out_of_stock':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'low_stock':
        return <TrendingDown className="h-5 w-5 text-yellow-500" />
      case 'overstock':
        return <Package className="h-5 w-5 text-blue-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />
    }
  }

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'border-red-200 bg-red-50'
      case 'medium':
        return 'border-yellow-200 bg-yellow-50'
      case 'low':
        return 'border-blue-200 bg-blue-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'high':
        return <Badge className="bg-red-100 text-red-800">عالي</Badge>
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">متوسط</Badge>
      case 'low':
        return <Badge className="bg-blue-100 text-blue-800">منخفض</Badge>
      default:
        return <Badge>{severity}</Badge>
    }
  }

  const alertsByType = {
    high: alerts.filter(a => a.severity === 'high'),
    medium: alerts.filter(a => a.severity === 'medium'),
    low: alerts.filter(a => a.severity === 'low')
  }

  const totalAlerts = alerts.length

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            تنبيهات المخزون
            {totalAlerts > 0 && (
              <Badge className="bg-red-500 text-white">
                {totalAlerts}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={loadData}>
              <RefreshCw className="h-4 w-4 ml-1" />
              تحديث
            </Button>
            {totalAlerts > 0 && (
              <Button variant="outline" size="sm" onClick={dismissAllAlerts}>
                إخفاء الكل
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {totalAlerts === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Package className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p>لا توجد تنبيهات مخزون حالياً</p>
            <p className="text-sm">جميع المنتجات في حالة جيدة</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* إحصائيات سريعة */}
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
                <div className="text-2xl font-bold text-red-600">{alertsByType.high.length}</div>
                <div className="text-sm text-red-600">عالي الأولوية</div>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="text-2xl font-bold text-yellow-600">{alertsByType.medium.length}</div>
                <div className="text-sm text-yellow-600">متوسط الأولوية</div>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="text-2xl font-bold text-blue-600">{alertsByType.low.length}</div>
                <div className="text-sm text-blue-600">منخفض الأولوية</div>
              </div>
            </div>

            {/* قائمة التنبيهات */}
            <div className="space-y-3">
              {alerts.map((alert) => (
                <div
                  key={alert.id}
                  className={`p-4 rounded-lg border ${getAlertColor(alert.severity)} transition-all`}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      {getAlertIcon(alert.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">
                          {alert.message}
                        </h4>
                        <div className="flex items-center gap-2">
                          {getSeverityBadge(alert.severity)}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => dismissAlert(alert.id)}
                            className="h-6 w-6 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>المنتج: {alert.product.name}</p>
                        <p>المخزون الحالي: {alert.product.stock} {alert.product.unit}</p>
                        <p>الحد الأدنى: {alert.product.minStock} {alert.product.unit}</p>
                        <p>القيمة: {formatCurrency(alert.product.stock * alert.product.cost)}</p>
                      </div>
                      <div className="flex items-center justify-between mt-3">
                        <span className="text-xs text-gray-400">
                          {new Date(alert.timestamp).toLocaleTimeString('ar-SA')}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.location.href = `/inventory?product=${alert.product.id}`}
                        >
                          <Eye className="h-3 w-3 ml-1" />
                          عرض المنتج
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
