'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  ArrowLeft, Search, Plus, Edit, Trash2, User, 
  Shield, Eye, EyeOff, Key, UserCheck, UserX,
  Crown, Settings, Clock, Mail, Phone
} from 'lucide-react'

export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState('الكل')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<any>(null)

  // بيانات وهمية للمستخدمين
  const [users, setUsers] = useState([
    {
      id: '1',
      name: '<PERSON>ح<PERSON><PERSON> محمد علي',
      email: '<EMAIL>',
      phone: '771234567',
      role: 'مدير النظام',
      status: 'نشط',
      lastLogin: new Date('2024-01-15T10:30:00'),
      createdAt: new Date('2023-06-15'),
      permissions: ['إدارة المستخدمين', 'إدارة المخزون', 'التقارير', 'نقطة البيع', 'الإعدادات'],
      avatar: null
    },
    {
      id: '2',
      name: 'سالم علي أحمد',
      email: '<EMAIL>',
      phone: '777654321',
      role: 'مدير المبيعات',
      status: 'نشط',
      lastLogin: new Date('2024-01-15T09:15:00'),
      createdAt: new Date('2023-08-20'),
      permissions: ['نقطة البيع', 'إدارة العملاء', 'التقارير'],
      avatar: null
    },
    {
      id: '3',
      name: 'فاطمة محمد',
      email: '<EMAIL>',
      phone: '773456789',
      role: 'محاسب',
      status: 'نشط',
      lastLogin: new Date('2024-01-14T16:45:00'),
      createdAt: new Date('2023-10-10'),
      permissions: ['التقارير', 'إدارة العملاء', 'الفواتير'],
      avatar: null
    },
    {
      id: '4',
      name: 'محمد عبدالله',
      email: '<EMAIL>',
      phone: '775987654',
      role: 'موظف مبيعات',
      status: 'معطل',
      lastLogin: new Date('2024-01-10T14:20:00'),
      createdAt: new Date('2023-12-01'),
      permissions: ['نقطة البيع'],
      avatar: null
    }
  ])

  const roles = ['الكل', 'مدير النظام', 'مدير المبيعات', 'محاسب', 'موظف مبيعات']

  // تصفية المستخدمين
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.phone.includes(searchTerm)
    const matchesRole = selectedRole === 'الكل' || user.role === selectedRole
    return matchesSearch && matchesRole
  })

  // إحصائيات المستخدمين
  const stats = {
    totalUsers: users.length,
    activeUsers: users.filter(u => u.status === 'نشط').length,
    adminUsers: users.filter(u => u.role === 'مدير النظام').length,
    onlineUsers: users.filter(u => {
      const timeDiff = Date.now() - u.lastLogin.getTime()
      return timeDiff < 30 * 60 * 1000 // آخر 30 دقيقة
    }).length
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'مدير النظام': return 'bg-red-100 text-red-800'
      case 'مدير المبيعات': return 'bg-blue-100 text-blue-800'
      case 'محاسب': return 'bg-green-100 text-green-800'
      case 'موظف مبيعات': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'مدير النظام': return <Crown className="h-4 w-4" />
      case 'مدير المبيعات': return <UserCheck className="h-4 w-4" />
      case 'محاسب': return <Settings className="h-4 w-4" />
      case 'موظف مبيعات': return <User className="h-4 w-4" />
      default: return <User className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    return status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
  }

  const formatLastLogin = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 30) return 'متصل الآن'
    if (diffMins < 60) return `منذ ${diffMins} دقيقة`
    if (diffHours < 24) return `منذ ${diffHours} ساعة`
    return `منذ ${diffDays} يوم`
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col" dir="rtl">
      {/* Header */}
      <div className="bg-white shadow-sm border-b p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">إدارة المستخدمين</h1>
          </div>
          
          <Button onClick={() => setShowAddModal(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            إضافة مستخدم
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="bg-white border-b p-4 flex-shrink-0">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي المستخدمين</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.totalUsers}</p>
                </div>
                <User className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">المستخدمون النشطون</p>
                  <p className="text-2xl font-bold text-green-600">{stats.activeUsers}</p>
                </div>
                <UserCheck className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">المديرون</p>
                  <p className="text-2xl font-bold text-red-600">{stats.adminUsers}</p>
                </div>
                <Crown className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">متصل الآن</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.onlineUsers}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white border-b p-4 flex-shrink-0">
        <div className="flex items-center gap-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="البحث بالاسم أو البريد أو الهاتف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>

          {/* Role Filter */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">الدور:</span>
            <div className="flex gap-1">
              {roles.map(role => (
                <Button
                  key={role}
                  variant={selectedRole === role ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedRole(role)}
                >
                  {role}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="flex-1 overflow-auto p-4">
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="text-right p-4 font-medium text-gray-700">المستخدم</th>
                    <th className="text-right p-4 font-medium text-gray-700">معلومات الاتصال</th>
                    <th className="text-right p-4 font-medium text-gray-700">الدور</th>
                    <th className="text-right p-4 font-medium text-gray-700">الحالة</th>
                    <th className="text-right p-4 font-medium text-gray-700">آخر دخول</th>
                    <th className="text-right p-4 font-medium text-gray-700">تاريخ الإنشاء</th>
                    <th className="text-right p-4 font-medium text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium">{user.name}</div>
                            <div className="text-sm text-gray-600">ID: {user.id}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2 text-sm">
                            <Mail className="h-3 w-3" />
                            {user.email}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Phone className="h-3 w-3" />
                            {user.phone}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getRoleColor(user.role)} flex items-center gap-1 w-fit`}>
                          {getRoleIcon(user.role)}
                          {user.role}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <Badge className={getStatusColor(user.status)}>
                          {user.status}
                        </Badge>
                      </td>
                      <td className="p-4 text-gray-600">
                        {formatLastLogin(user.lastLogin)}
                      </td>
                      <td className="p-4 text-gray-600">
                        {user.createdAt.toLocaleDateString('ar-SA')}
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedUser(user)
                              setShowEditModal(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Key className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            className={user.status === 'نشط' ? 'text-red-600' : 'text-green-600'}
                          >
                            {user.status === 'نشط' ? <UserX className="h-4 w-4" /> : <UserCheck className="h-4 w-4" />}
                          </Button>
                          <Button variant="ghost" size="sm" className="text-red-600">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
