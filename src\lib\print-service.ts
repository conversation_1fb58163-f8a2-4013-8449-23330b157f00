// خدمة الطباعة المتقدمة
import { Sale, Product, Customer, Return } from './real-storage'

export interface PrintOptions {
  showLogo?: boolean
  showHeader?: boolean
  showFooter?: boolean
  paperSize?: 'A4' | 'A5' | 'thermal' | 'custom'
  orientation?: 'portrait' | 'landscape'
  fontSize?: 'small' | 'medium' | 'large'
  language?: 'ar' | 'en'
  copies?: number
}

export interface CompanyInfo {
  name: string
  nameEn?: string
  address: string
  phone: string
  email?: string
  website?: string
  taxNumber?: string
  crNumber?: string
  logo?: string
}

class PrintService {
  private defaultCompanyInfo: CompanyInfo = {
    name: 'تجارة الحديد والمعدات',
    nameEn: 'Steel & Equipment Trading',
    address: 'صنعاء - اليمن',
    phone: '+*********** 456',
    email: '<EMAIL>',
    website: 'www.steel-trade.com',
    taxNumber: '*********',
    crNumber: '*********'
  }

  private getCompanyInfo(): CompanyInfo {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('company_info')
      if (saved) {
        return { ...this.defaultCompanyInfo, ...JSON.parse(saved) }
      }
    }
    return this.defaultCompanyInfo
  }

  public setCompanyInfo(info: Partial<CompanyInfo>): void {
    if (typeof window !== 'undefined') {
      const current = this.getCompanyInfo()
      const updated = { ...current, ...info }
      localStorage.setItem('company_info', JSON.stringify(updated))
    }
  }

  private getStyles(options: PrintOptions): string {
    const fontSize = options.fontSize === 'small' ? '12px' : 
                    options.fontSize === 'large' ? '16px' : '14px'
    
    return `
      <style>
        @media print {
          @page {
            size: ${options.paperSize === 'A5' ? 'A5' : 'A4'} ${options.orientation || 'portrait'};
            margin: ${options.paperSize === 'thermal' ? '5mm' : '15mm'};
          }
          body { 
            font-family: 'Arial', sans-serif; 
            font-size: ${fontSize};
            line-height: 1.4;
            color: #000;
            direction: ${options.language === 'en' ? 'ltr' : 'rtl'};
          }
          .no-print { display: none !important; }
        }
        
        body { 
          font-family: 'Arial', sans-serif; 
          font-size: ${fontSize};
          line-height: 1.4;
          margin: 0;
          padding: 20px;
          direction: ${options.language === 'en' ? 'ltr' : 'rtl'};
        }
        
        .header {
          text-align: center;
          border-bottom: 2px solid #333;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        
        .logo {
          max-width: 150px;
          max-height: 80px;
          margin-bottom: 10px;
        }
        
        .company-name {
          font-size: 24px;
          font-weight: bold;
          color: #2563eb;
          margin: 10px 0;
        }
        
        .company-details {
          font-size: 12px;
          color: #666;
          margin: 5px 0;
        }
        
        .document-title {
          font-size: 20px;
          font-weight: bold;
          text-align: center;
          margin: 20px 0;
          color: #1f2937;
        }
        
        .info-section {
          display: flex;
          justify-content: space-between;
          margin: 20px 0;
          padding: 15px;
          background-color: #f8f9fa;
          border-radius: 5px;
        }
        
        .info-group {
          flex: 1;
        }
        
        .info-label {
          font-weight: bold;
          color: #374151;
        }
        
        .info-value {
          margin: 5px 0;
        }
        
        .table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
        }
        
        .table th,
        .table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: ${options.language === 'en' ? 'left' : 'right'};
        }
        
        .table th {
          background-color: #f1f5f9;
          font-weight: bold;
          color: #1e293b;
        }
        
        .table tr:nth-child(even) {
          background-color: #f8fafc;
        }
        
        .totals-section {
          margin-top: 30px;
          padding: 20px;
          background-color: #f8f9fa;
          border-radius: 5px;
        }
        
        .total-row {
          display: flex;
          justify-content: space-between;
          margin: 8px 0;
          padding: 5px 0;
        }
        
        .total-row.final {
          border-top: 2px solid #333;
          font-weight: bold;
          font-size: 18px;
          color: #1f2937;
        }
        
        .footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #ddd;
          text-align: center;
          font-size: 12px;
          color: #666;
        }
        
        .signature-section {
          display: flex;
          justify-content: space-between;
          margin-top: 40px;
        }
        
        .signature-box {
          text-align: center;
          width: 200px;
        }
        
        .signature-line {
          border-top: 1px solid #333;
          margin-top: 40px;
          padding-top: 5px;
        }
        
        .notes {
          margin: 20px 0;
          padding: 15px;
          background-color: #fef3c7;
          border-radius: 5px;
          border-left: 4px solid #f59e0b;
        }
        
        .watermark {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(-45deg);
          font-size: 72px;
          color: rgba(0,0,0,0.1);
          z-index: -1;
          pointer-events: none;
        }
      </style>
    `
  }

  private getHeader(companyInfo: CompanyInfo, options: PrintOptions): string {
    if (!options.showHeader) return ''
    
    return `
      <div class="header">
        ${options.showLogo && companyInfo.logo ? `<img src="${companyInfo.logo}" alt="Logo" class="logo">` : ''}
        <div class="company-name">${companyInfo.name}</div>
        ${companyInfo.nameEn ? `<div class="company-name" style="font-size: 18px;">${companyInfo.nameEn}</div>` : ''}
        <div class="company-details">${companyInfo.address}</div>
        <div class="company-details">هاتف: ${companyInfo.phone}</div>
        ${companyInfo.email ? `<div class="company-details">البريد: ${companyInfo.email}</div>` : ''}
        ${companyInfo.website ? `<div class="company-details">الموقع: ${companyInfo.website}</div>` : ''}
        <div class="company-details">
          ${companyInfo.taxNumber ? `الرقم الضريبي: ${companyInfo.taxNumber}` : ''}
          ${companyInfo.crNumber ? ` | السجل التجاري: ${companyInfo.crNumber}` : ''}
        </div>
      </div>
    `
  }

  private getFooter(options: PrintOptions): string {
    if (!options.showFooter) return ''
    
    return `
      <div class="footer">
        <p>شكراً لتعاملكم معنا</p>
        <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</p>
      </div>
    `
  }

  public printInvoice(sale: Sale, options: PrintOptions = {}): void {
    const defaultOptions: PrintOptions = {
      showLogo: true,
      showHeader: true,
      showFooter: true,
      paperSize: 'A4',
      orientation: 'portrait',
      fontSize: 'medium',
      language: 'ar',
      copies: 1
    }
    
    const printOptions = { ...defaultOptions, ...options }
    const companyInfo = this.getCompanyInfo()
    
    const content = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>فاتورة رقم ${sale.id.slice(-6)}</title>
        ${this.getStyles(printOptions)}
      </head>
      <body>
        ${this.getHeader(companyInfo, printOptions)}
        
        <div class="document-title">فاتورة مبيعات</div>
        
        <div class="info-section">
          <div class="info-group">
            <div class="info-label">معلومات الفاتورة:</div>
            <div class="info-value">رقم الفاتورة: ${sale.id.slice(-6)}</div>
            <div class="info-value">التاريخ: ${new Date(sale.createdAt).toLocaleDateString('ar-SA')}</div>
            <div class="info-value">الوقت: ${new Date(sale.createdAt).toLocaleTimeString('ar-SA')}</div>
          </div>
          <div class="info-group">
            <div class="info-label">معلومات العميل:</div>
            <div class="info-value">الاسم: ${sale.customerName}</div>
            <div class="info-value">نوع الدفع: ${this.getPaymentTypeLabel(sale.paymentType)}</div>
            <div class="info-value">الحالة: ${this.getStatusLabel(sale.status)}</div>
          </div>
        </div>
        
        <table class="table">
          <thead>
            <tr>
              <th>م</th>
              <th>المنتج</th>
              <th>الكمية</th>
              <th>الوحدة</th>
              <th>السعر</th>
              <th>الإجمالي</th>
            </tr>
          </thead>
          <tbody>
            ${sale.items.map((item, index) => `
              <tr>
                <td>${index + 1}</td>
                <td>${item.productName}</td>
                <td>${item.quantity.toLocaleString()}</td>
                <td>وحدة</td>
                <td>${item.unitPrice.toLocaleString()} ر.ي</td>
                <td>${item.total.toLocaleString()} ر.ي</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        
        <div class="totals-section">
          <div class="total-row">
            <span>المجموع الفرعي:</span>
            <span>${sale.subtotal.toLocaleString()} ر.ي</span>
          </div>
          ${sale.discount > 0 ? `
            <div class="total-row">
              <span>الخصم:</span>
              <span>-${sale.discount.toLocaleString()} ر.ي</span>
            </div>
          ` : ''}
          <div class="total-row">
            <span>الضريبة:</span>
            <span>${sale.tax.toLocaleString()} ر.ي</span>
          </div>
          <div class="total-row final">
            <span>الإجمالي النهائي:</span>
            <span>${sale.total.toLocaleString()} ر.ي</span>
          </div>
          <div class="total-row">
            <span>المبلغ المدفوع:</span>
            <span>${sale.paidAmount.toLocaleString()} ر.ي</span>
          </div>
          ${sale.remainingAmount > 0 ? `
            <div class="total-row" style="color: #dc2626;">
              <span>المبلغ المتبقي:</span>
              <span>${sale.remainingAmount.toLocaleString()} ر.ي</span>
            </div>
          ` : ''}
        </div>
        
        ${sale.notes ? `
          <div class="notes">
            <strong>ملاحظات:</strong><br>
            ${sale.notes}
          </div>
        ` : ''}
        
        <div class="signature-section">
          <div class="signature-box">
            <div class="signature-line">توقيع العميل</div>
          </div>
          <div class="signature-box">
            <div class="signature-line">توقيع المندوب</div>
          </div>
        </div>
        
        ${this.getFooter(printOptions)}
      </body>
      </html>
    `
    
    this.openPrintWindow(content, printOptions.copies || 1)
  }

  private getPaymentTypeLabel(type: string): string {
    switch (type) {
      case 'cash': return 'نقدي'
      case 'credit': return 'آجل'
      case 'partial': return 'جزئي'
      default: return type
    }
  }

  private getStatusLabel(status: string): string {
    switch (status) {
      case 'completed': return 'مكتملة'
      case 'pending': return 'معلقة'
      case 'cancelled': return 'ملغية'
      default: return status
    }
  }

  public printProductsList(products: Product[], options: PrintOptions = {}): void {
    const defaultOptions: PrintOptions = {
      showLogo: true,
      showHeader: true,
      showFooter: true,
      paperSize: 'A4',
      orientation: 'landscape',
      fontSize: 'small',
      language: 'ar'
    }

    const printOptions = { ...defaultOptions, ...options }
    const companyInfo = this.getCompanyInfo()

    const content = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>قائمة المنتجات</title>
        ${this.getStyles(printOptions)}
      </head>
      <body>
        ${this.getHeader(companyInfo, printOptions)}

        <div class="document-title">قائمة المنتجات</div>

        <div class="info-section">
          <div class="info-group">
            <div class="info-value">عدد المنتجات: ${products.length}</div>
            <div class="info-value">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</div>
          </div>
        </div>

        <table class="table">
          <thead>
            <tr>
              <th>م</th>
              <th>اسم المنتج</th>
              <th>الفئة</th>
              <th>المخزون</th>
              <th>الحد الأدنى</th>
              <th>السعر</th>
              <th>الحالة</th>
            </tr>
          </thead>
          <tbody>
            ${products.map((product, index) => `
              <tr>
                <td>${index + 1}</td>
                <td>${product.name}</td>
                <td>${product.category}</td>
                <td>${product.stock.toLocaleString()}</td>
                <td>${product.minStock.toLocaleString()}</td>
                <td>${product.price.toLocaleString()} ر.ي</td>
                <td>${product.stock <= product.minStock ? 'مخزون منخفض' : 'متوفر'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        ${this.getFooter(printOptions)}
      </body>
      </html>
    `

    this.openPrintWindow(content, printOptions.copies || 1)
  }

  public printCustomersList(customers: Customer[], options: PrintOptions = {}): void {
    const defaultOptions: PrintOptions = {
      showLogo: true,
      showHeader: true,
      showFooter: true,
      paperSize: 'A4',
      orientation: 'portrait',
      fontSize: 'small',
      language: 'ar'
    }

    const printOptions = { ...defaultOptions, ...options }
    const companyInfo = this.getCompanyInfo()

    const content = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>قائمة العملاء</title>
        ${this.getStyles(printOptions)}
      </head>
      <body>
        ${this.getHeader(companyInfo, printOptions)}

        <div class="document-title">قائمة العملاء</div>

        <div class="info-section">
          <div class="info-group">
            <div class="info-value">عدد العملاء: ${customers.length}</div>
            <div class="info-value">إجمالي الديون: ${customers.reduce((sum, c) => sum + c.currentDebt, 0).toLocaleString()} ر.ي</div>
          </div>
        </div>

        <table class="table">
          <thead>
            <tr>
              <th>م</th>
              <th>اسم العميل</th>
              <th>الهاتف</th>
              <th>النوع</th>
              <th>إجمالي المشتريات</th>
              <th>الدين الحالي</th>
              <th>الحالة</th>
            </tr>
          </thead>
          <tbody>
            ${customers.map((customer, index) => `
              <tr>
                <td>${index + 1}</td>
                <td>${customer.name}</td>
                <td>${customer.phone}</td>
                <td>${customer.type === 'individual' ? 'فرد' : 'شركة'}</td>
                <td>${customer.totalPurchases.toLocaleString()} ر.ي</td>
                <td>${customer.currentDebt.toLocaleString()} ر.ي</td>
                <td>${customer.status === 'active' ? 'نشط' : 'غير نشط'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        ${this.getFooter(printOptions)}
      </body>
      </html>
    `

    this.openPrintWindow(content, printOptions.copies || 1)
  }

  public printSalesReport(sales: Sale[], startDate?: Date, endDate?: Date, options: PrintOptions = {}): void {
    const defaultOptions: PrintOptions = {
      showLogo: true,
      showHeader: true,
      showFooter: true,
      paperSize: 'A4',
      orientation: 'landscape',
      fontSize: 'small',
      language: 'ar'
    }

    const printOptions = { ...defaultOptions, ...options }
    const companyInfo = this.getCompanyInfo()

    const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0)
    const totalPaid = sales.reduce((sum, sale) => sum + sale.paidAmount, 0)
    const totalRemaining = sales.reduce((sum, sale) => sum + sale.remainingAmount, 0)

    const content = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تقرير المبيعات</title>
        ${this.getStyles(printOptions)}
      </head>
      <body>
        ${this.getHeader(companyInfo, printOptions)}

        <div class="document-title">تقرير المبيعات</div>

        <div class="info-section">
          <div class="info-group">
            <div class="info-value">عدد الفواتير: ${sales.length}</div>
            <div class="info-value">إجمالي المبيعات: ${totalSales.toLocaleString()} ر.ي</div>
            <div class="info-value">إجمالي المدفوع: ${totalPaid.toLocaleString()} ر.ي</div>
            <div class="info-value">إجمالي المتبقي: ${totalRemaining.toLocaleString()} ر.ي</div>
          </div>
          <div class="info-group">
            ${startDate ? `<div class="info-value">من تاريخ: ${startDate.toLocaleDateString('ar-SA')}</div>` : ''}
            ${endDate ? `<div class="info-value">إلى تاريخ: ${endDate.toLocaleDateString('ar-SA')}</div>` : ''}
            <div class="info-value">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</div>
          </div>
        </div>

        <table class="table">
          <thead>
            <tr>
              <th>رقم الفاتورة</th>
              <th>العميل</th>
              <th>التاريخ</th>
              <th>المبلغ</th>
              <th>المدفوع</th>
              <th>المتبقي</th>
              <th>نوع الدفع</th>
              <th>الحالة</th>
            </tr>
          </thead>
          <tbody>
            ${sales.map(sale => `
              <tr>
                <td>${sale.id.slice(-6)}</td>
                <td>${sale.customerName}</td>
                <td>${new Date(sale.createdAt).toLocaleDateString('ar-SA')}</td>
                <td>${sale.total.toLocaleString()} ر.ي</td>
                <td>${sale.paidAmount.toLocaleString()} ر.ي</td>
                <td>${sale.remainingAmount.toLocaleString()} ر.ي</td>
                <td>${this.getPaymentTypeLabel(sale.paymentType)}</td>
                <td>${this.getStatusLabel(sale.status)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        ${this.getFooter(printOptions)}
      </body>
      </html>
    `

    this.openPrintWindow(content, printOptions.copies || 1)
  }

  public printThermalReceipt(sale: Sale): void {
    const companyInfo = this.getCompanyInfo()

    const content = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>إيصال</title>
        <style>
          @media print {
            @page { size: 80mm auto; margin: 0; }
            body { margin: 0; }
          }
          body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.2;
            width: 80mm;
            margin: 0;
            padding: 5mm;
          }
          .center { text-align: center; }
          .bold { font-weight: bold; }
          .line { border-top: 1px dashed #000; margin: 5px 0; }
          .item-row { display: flex; justify-content: space-between; }
        </style>
      </head>
      <body>
        <div class="center bold">${companyInfo.name}</div>
        <div class="center">${companyInfo.address}</div>
        <div class="center">${companyInfo.phone}</div>
        <div class="line"></div>

        <div>فاتورة: ${sale.id.slice(-6)}</div>
        <div>التاريخ: ${new Date(sale.createdAt).toLocaleDateString('ar-SA')}</div>
        <div>العميل: ${sale.customerName}</div>
        <div class="line"></div>

        ${sale.items.map(item => `
          <div class="item-row">
            <span>${item.productName}</span>
          </div>
          <div class="item-row">
            <span>${item.quantity} × ${item.unitPrice.toLocaleString()}</span>
            <span>${item.total.toLocaleString()}</span>
          </div>
        `).join('')}

        <div class="line"></div>
        <div class="item-row bold">
          <span>الإجمالي:</span>
          <span>${sale.total.toLocaleString()} ر.ي</span>
        </div>
        <div class="item-row">
          <span>المدفوع:</span>
          <span>${sale.paidAmount.toLocaleString()} ر.ي</span>
        </div>
        ${sale.remainingAmount > 0 ? `
          <div class="item-row">
            <span>المتبقي:</span>
            <span>${sale.remainingAmount.toLocaleString()} ر.ي</span>
          </div>
        ` : ''}

        <div class="line"></div>
        <div class="center">شكراً لتعاملكم معنا</div>
        <div class="center">${new Date().toLocaleTimeString('ar-SA')}</div>
      </body>
      </html>
    `

    this.openPrintWindow(content, 1)
  }

  private openPrintWindow(content: string, copies: number = 1): void {
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(content)
      printWindow.document.close()

      printWindow.onload = () => {
        for (let i = 0; i < copies; i++) {
          printWindow.print()
        }
        // Don't close automatically to allow user to review
        // printWindow.close()
      }
    }
  }
}

export const printService = new PrintService()
