'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  ArrowLeft, Search, Plus, Edit, Trash2, AlertTriangle,
  Package, TrendingDown, TrendingUp, Filter, Download,
  Eye, BarChart3, RefreshCw, CheckCircle, XCircle, X
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { realDataManager, Product } from '@/lib/real-storage'

export default function InventoryPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('الكل')
  const [selectedStatus, setSelectedStatus] = useState('الكل')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [products, setProducts] = useState<Product[]>([])

  useEffect(() => {
    realDataManager.initializeData()
    loadProducts()
  }, [])

  const loadProducts = () => {
    setProducts(realDataManager.getProducts())
  }

  // تصفية المنتجات
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.barcode?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'الكل' || product.category === selectedCategory
    const matchesStatus = selectedStatus === 'الكل' ||
                         (selectedStatus === 'متوفر' && product.stock > product.minStock) ||
                         (selectedStatus === 'مخزون منخفض' && product.stock <= product.minStock) ||
                         (selectedStatus === 'نفد المخزون' && product.stock === 0)

    return matchesSearch && matchesCategory && matchesStatus
  })

  // الحصول على الفئات المتاحة
  const categories = ['الكل', ...new Set(products.map(p => p.category).filter(Boolean))]

  // إحصائيات المخزون
  const totalProducts = products.length
  const totalValue = products.reduce((sum, product) => sum + (product.price * product.stock), 0)
  const lowStockCount = products.filter(p => p.stock <= p.minStock).length
  const outOfStockCount = products.filter(p => p.stock === 0).length

  const getStockStatus = (product: Product) => {
    if (product.stock === 0) return { label: 'نفد المخزون', color: 'bg-red-100 text-red-800' }
    if (product.stock <= product.minStock) return { label: 'مخزون منخفض', color: 'bg-yellow-100 text-yellow-800' }
    return { label: 'متوفر', color: 'bg-green-100 text-green-800' }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">إدارة المخزون</h1>
        <Button onClick={() => setShowAddModal(true)}>
          <Plus className="mr-2 h-4 w-4" />
          إضافة منتج
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المنتجات</p>
                <p className="text-2xl font-bold">{totalProducts}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">قيمة المخزون</p>
                <p className="text-2xl font-bold">{totalValue.toLocaleString()} ر.ي</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">مخزون منخفض</p>
                <p className="text-2xl font-bold text-yellow-600">{lowStockCount}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">نفد المخزون</p>
                <p className="text-2xl font-bold text-red-600">{outOfStockCount}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في المنتجات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="الكل">جميع الحالات</option>
              <option value="متوفر">متوفر</option>
              <option value="مخزون منخفض">مخزون منخفض</option>
              <option value="نفد المخزون">نفد المخزون</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة المنتجات ({filteredProducts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredProducts.length === 0 ? (
            <p className="text-center text-gray-500 py-8">لا توجد منتجات</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-2">المنتج</th>
                    <th className="text-right p-2">الفئة</th>
                    <th className="text-right p-2">المخزون</th>
                    <th className="text-right p-2">الحد الأدنى</th>
                    <th className="text-right p-2">السعر</th>
                    <th className="text-right p-2">الحالة</th>
                    <th className="text-right p-2">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProducts.map((product) => {
                    const status = getStockStatus(product)
                    return (
                      <tr key={product.id} className="border-b hover:bg-gray-50">
                        <td className="p-2">
                          <div>
                            <p className="font-medium">{product.name}</p>
                            {product.barcode && (
                              <p className="text-sm text-gray-500">{product.barcode}</p>
                            )}
                          </div>
                        </td>
                        <td className="p-2">{product.category || 'غير محدد'}</td>
                        <td className="p-2">
                          <span className={product.stock <= product.minStock ? 'text-red-600 font-semibold' : ''}>
                            {product.stock}
                          </span>
                        </td>
                        <td className="p-2">{product.minStock}</td>
                        <td className="p-2">{product.price.toLocaleString()} ر.ي</td>
                        <td className="p-2">
                          <Badge className={status.color}>{status.label}</Badge>
                        </td>
                        <td className="p-2">
                          <div className="flex gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedProduct(product)
                                setShowEditModal(true)
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
