import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core'
import { relations } from 'drizzle-orm'

// جدول الشركات/المؤسسات
export const companies = sqliteTable('companies', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  email: text('email'),
  phone: text('phone'),
  address: text('address'),
  taxNumber: text('tax_number'),
  logo: text('logo'),
  userId: text('user_id').notNull(), // Clerk user ID
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// جدول العملاء (محدث لتجارة الحديد)
export const customers = sqliteTable('customers', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  company: text('company'),
  email: text('email'),
  phone: text('phone').notNull(),
  address: text('address'),
  taxNumber: text('tax_number'),
  type: text('type').default('individual').notNull(), // individual, company
  status: text('status').default('active').notNull(), // active, inactive
  creditLimit: real('credit_limit').default(0).notNull(),
  currentDebt: real('current_debt').default(0).notNull(),
  totalPurchases: real('total_purchases').default(0).notNull(),
  lastPurchase: integer('last_purchase', { mode: 'timestamp' }),
  companyId: text('company_id').references(() => companies.id).notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true).notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// جدول المنتجات/الخدمات (محدث للحديد والمعدات)
export const products = sqliteTable('products', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  price: real('price').notNull(),
  cost: real('cost').notNull(),
  unit: text('unit').default('طن'),
  sku: text('sku'),
  barcode: text('barcode'),
  category: text('category'),
  stock: integer('stock').default(0).notNull(),
  minStock: integer('min_stock').default(0).notNull(),
  supplier: text('supplier'),
  location: text('location'),
  image: text('image'),
  companyId: text('company_id').references(() => companies.id).notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true).notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// جدول المبيعات (نقطة البيع)
export const sales = sqliteTable('sales', {
  id: text('id').primaryKey(),
  customerId: text('customer_id').references(() => customers.id),
  customerName: text('customer_name').notNull(),
  subtotal: real('subtotal').notNull(),
  discount: real('discount').default(0).notNull(),
  tax: real('tax').default(0).notNull(),
  total: real('total').notNull(),
  paymentType: text('payment_type').notNull(), // cash, credit, partial
  paidAmount: real('paid_amount').notNull(),
  remainingAmount: real('remaining_amount').default(0).notNull(),
  status: text('status').default('completed').notNull(), // completed, pending, cancelled
  notes: text('notes'),
  companyId: text('company_id').references(() => companies.id).notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// جدول عناصر المبيعات
export const saleItems = sqliteTable('sale_items', {
  id: text('id').primaryKey(),
  saleId: text('sale_id').references(() => sales.id).notNull(),
  productId: text('product_id').references(() => products.id).notNull(),
  productName: text('product_name').notNull(),
  quantity: real('quantity').notNull(),
  unitPrice: real('unit_price').notNull(),
  total: real('total').notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// جدول المرتجعات
export const returns = sqliteTable('returns', {
  id: text('id').primaryKey(),
  saleId: text('sale_id').references(() => sales.id).notNull(),
  customerId: text('customer_id').references(() => customers.id),
  customerName: text('customer_name').notNull(),
  total: real('total').notNull(),
  reason: text('reason').notNull(),
  status: text('status').default('completed').notNull(),
  notes: text('notes'),
  companyId: text('company_id').references(() => companies.id).notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// جدول عناصر المرتجعات
export const returnItems = sqliteTable('return_items', {
  id: text('id').primaryKey(),
  returnId: text('return_id').references(() => returns.id).notNull(),
  productId: text('product_id').references(() => products.id).notNull(),
  productName: text('product_name').notNull(),
  quantity: real('quantity').notNull(),
  unitPrice: real('unit_price').notNull(),
  total: real('total').notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// جدول حركات المخزون
export const stockMovements = sqliteTable('stock_movements', {
  id: text('id').primaryKey(),
  productId: text('product_id').references(() => products.id).notNull(),
  type: text('type').notNull(), // in, out, adjustment
  quantity: integer('quantity').notNull(),
  reason: text('reason').notNull(),
  reference: text('reference'),
  companyId: text('company_id').references(() => companies.id).notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// جدول الموردين
export const suppliers = sqliteTable('suppliers', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  company: text('company'),
  email: text('email'),
  phone: text('phone').notNull(),
  address: text('address'),
  taxNumber: text('tax_number'),
  status: text('status').default('active').notNull(),
  totalPurchases: real('total_purchases').default(0).notNull(),
  lastPurchase: integer('last_purchase', { mode: 'timestamp' }),
  companyId: text('company_id').references(() => companies.id).notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// جدول الفواتير
export const invoices = sqliteTable('invoices', {
  id: text('id').primaryKey(),
  invoiceNumber: text('invoice_number').notNull(),
  customerId: text('customer_id').references(() => customers.id).notNull(),
  companyId: text('company_id').references(() => companies.id).notNull(),
  issueDate: integer('issue_date', { mode: 'timestamp' }).notNull(),
  dueDate: integer('due_date', { mode: 'timestamp' }).notNull(),
  subtotal: real('subtotal').notNull(),
  taxAmount: real('tax_amount').default(0).notNull(),
  discountAmount: real('discount_amount').default(0).notNull(),
  total: real('total').notNull(),
  status: text('status').default('draft').notNull(), // draft, sent, paid, overdue, cancelled
  notes: text('notes'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// جدول عناصر الفاتورة
export const invoiceItems = sqliteTable('invoice_items', {
  id: text('id').primaryKey(),
  invoiceId: text('invoice_id').references(() => invoices.id).notNull(),
  productId: text('product_id').references(() => products.id),
  description: text('description').notNull(),
  quantity: real('quantity').notNull(),
  unitPrice: real('unit_price').notNull(),
  total: real('total').notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// جدول المدفوعات
export const payments = sqliteTable('payments', {
  id: text('id').primaryKey(),
  invoiceId: text('invoice_id').references(() => invoices.id).notNull(),
  amount: real('amount').notNull(),
  paymentDate: integer('payment_date', { mode: 'timestamp' }).notNull(),
  paymentMethod: text('payment_method').notNull(), // cash, bank_transfer, credit_card, check
  reference: text('reference'),
  notes: text('notes'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// جدول المصروفات
export const expenses = sqliteTable('expenses', {
  id: text('id').primaryKey(),
  companyId: text('company_id').references(() => companies.id).notNull(),
  description: text('description').notNull(),
  amount: real('amount').notNull(),
  category: text('category').notNull(),
  expenseDate: integer('expense_date', { mode: 'timestamp' }).notNull(),
  paymentMethod: text('payment_method'),
  receipt: text('receipt'), // URL to receipt image
  notes: text('notes'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// Types
export type Company = typeof companies.$inferSelect
export type NewCompany = typeof companies.$inferInsert
export type Customer = typeof customers.$inferSelect
export type NewCustomer = typeof customers.$inferInsert
export type Product = typeof products.$inferSelect
export type NewProduct = typeof products.$inferInsert
export type Sale = typeof sales.$inferSelect
export type NewSale = typeof sales.$inferInsert
export type SaleItem = typeof saleItems.$inferSelect
export type NewSaleItem = typeof saleItems.$inferInsert
export type Return = typeof returns.$inferSelect
export type NewReturn = typeof returns.$inferInsert
export type ReturnItem = typeof returnItems.$inferSelect
export type NewReturnItem = typeof returnItems.$inferInsert
export type StockMovement = typeof stockMovements.$inferSelect
export type NewStockMovement = typeof stockMovements.$inferInsert
export type Supplier = typeof suppliers.$inferSelect
export type NewSupplier = typeof suppliers.$inferInsert
export type Invoice = typeof invoices.$inferSelect
export type NewInvoice = typeof invoices.$inferInsert
export type InvoiceItem = typeof invoiceItems.$inferSelect
export type NewInvoiceItem = typeof invoiceItems.$inferInsert
export type Payment = typeof payments.$inferSelect
export type NewPayment = typeof payments.$inferInsert
export type Expense = typeof expenses.$inferSelect
export type NewExpense = typeof expenses.$inferInsert
