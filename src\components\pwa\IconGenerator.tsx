'use client'

import { useEffect } from 'react'

// مولد أيقونات PWA ديناميكي
export default function IconGenerator() {
  useEffect(() => {
    generateIcons()
  }, [])

  const generateIcons = () => {
    try {
      const sizes = [72, 96, 128, 144, 152, 192, 384, 512]

      sizes.forEach(size => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        if (!ctx) return

        canvas.width = size
        canvas.height = size

        // خلفية متدرجة
        const gradient = ctx.createLinearGradient(0, 0, size, size)
        gradient.addColorStop(0, '#3B82F6') // أزرق
        gradient.addColorStop(1, '#1E40AF') // أزرق داكن

        ctx.fillStyle = gradient
        ctx.fillRect(0, 0, size, size)

        // إضافة النص
        ctx.fillStyle = 'white'
        ctx.font = `bold ${size * 0.4}px Arial`
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText('ح', size / 2, size / 2)

        // إضافة حدود
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
        ctx.lineWidth = size * 0.02
        ctx.strokeRect(0, 0, size, size)
      })

      // إنشاء favicon ديناميكي
      generateFavicon()
    } catch (error) {
      console.warn('Error generating icons:', error)
    }
  }

  const generateFavicon = () => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = 32
    canvas.height = 32
    
    // خلفية
    const gradient = ctx.createLinearGradient(0, 0, 32, 32)
    gradient.addColorStop(0, '#3B82F6')
    gradient.addColorStop(1, '#1E40AF')
    
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, 32, 32)
    
    // النص
    ctx.fillStyle = 'white'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText('ح', 16, 16)
    
    // تحديث favicon
    const link = document.querySelector("link[rel*='icon']") as HTMLLinkElement || document.createElement('link')
    link.type = 'image/png'
    link.rel = 'shortcut icon'
    link.href = canvas.toDataURL('image/png')
    document.getElementsByTagName('head')[0].appendChild(link)
  }

  return null // مكون غير مرئي
}
