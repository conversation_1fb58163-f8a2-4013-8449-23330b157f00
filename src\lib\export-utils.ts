// مكتبة تصدير البيانات المحسنة

export interface ExportData {
  headers: string[]
  rows: (string | number)[][]
  title: string
  filename: string
}

export interface ExportOptions {
  includeTimestamp?: boolean
  includeCompanyInfo?: boolean
  customFooter?: string
  dateRange?: {
    from: Date
    to: Date
  }
}

// تصدير إلى CSV محسن
export function exportToCSV(data: ExportData, options: ExportOptions = {}) {
  let csvContent = ''

  // إضافة معلومات الشركة إذا كان مطلوباً
  if (options.includeCompanyInfo) {
    csvContent += 'تجارة الحديد والمعدات\n'
    csvContent += 'صنعاء، اليمن\n'
    csvContent += 'هاتف: +967-1-234567\n'
    csvContent += '\n'
  }

  // إضافة العنوان والتاريخ
  csvContent += `${data.title}\n`

  if (options.includeTimestamp) {
    csvContent += `تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}\n`
  }

  if (options.dateRange) {
    csvContent += `الفترة: من ${options.dateRange.from.toLocaleDateString('ar-SA')} إلى ${options.dateRange.to.toLocaleDateString('ar-SA')}\n`
  }

  csvContent += '\n'

  // إضافة البيانات
  csvContent += data.headers.join(',') + '\n'
  csvContent += data.rows.map(row =>
    row.map(cell => `"${cell}"`).join(',')
  ).join('\n')

  // إضافة تذييل مخصص
  if (options.customFooter) {
    csvContent += '\n\n' + options.customFooter
  }

  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${data.filename}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

// تصدير إلى Excel (محاكاة - يحتاج مكتبة xlsx)
export function exportToExcel(data: ExportData, options: ExportOptions = {}) {
  try {
    // في التطبيق الحقيقي، ستحتاج لاستخدام مكتبة xlsx
    console.log('تصدير Excel قيد التطوير')

    // محاكاة تصدير Excel كـ CSV مع تنسيق أفضل
    const excelData: ExportData = {
      ...data,
      filename: data.filename.replace('.csv', '.xlsx')
    }

    exportToCSV(excelData, options)

    /*
    // مثال على استخدام مكتبة xlsx:
    import * as XLSX from 'xlsx'

    const ws = XLSX.utils.aoa_to_sheet([
      data.headers,
      ...data.rows
    ])

    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'البيانات')

    XLSX.writeFile(wb, `${data.filename}.xlsx`)
    */

  } catch (error) {
    console.error('خطأ في تصدير Excel:', error)
    // العودة إلى CSV في حالة الخطأ
    exportToCSV(data, options)
  }
}

// تصدير إلى JSON محسن
export function exportToJSON(data: any, filename: string, options: ExportOptions = {}) {
  const exportData = {
    metadata: {
      title: filename,
      exportDate: new Date().toISOString(),
      version: '1.0.0',
      ...(options.dateRange && {
        dateRange: {
          from: options.dateRange.from.toISOString(),
          to: options.dateRange.to.toISOString()
        }
      })
    },
    data: data
  }

  const jsonContent = JSON.stringify(exportData, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json' })
  const link = document.createElement('a')

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.json`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

// تصدير تقرير المبيعات محسن
export function exportSalesReport(sales: any[], period: string, format: 'csv' | 'excel' | 'json' = 'csv', options: ExportOptions = {}) {
  const data: ExportData = {
    title: `تقرير المبيعات - ${period}`,
    filename: `sales-report-${period}-${new Date().toISOString().split('T')[0]}`,
    headers: [
      'رقم المبيعة',
      'اسم العميل',
      'التاريخ',
      'المجموع الفرعي',
      'الخصم',
      'الضريبة',
      'الإجمالي',
      'طريقة الدفع',
      'المبلغ المدفوع',
      'المبلغ المتبقي',
      'الحالة'
    ],
    rows: sales.map(sale => [
      sale.id.slice(-8),
      sale.customerName,
      new Date(sale.createdAt).toLocaleDateString('ar-SA'),
      sale.subtotal,
      sale.discount,
      sale.tax,
      sale.total,
      sale.paymentType === 'cash' ? 'نقدي' : sale.paymentType === 'credit' ? 'آجل' : 'جزئي',
      sale.paidAmount,
      sale.remainingAmount,
      sale.status === 'completed' ? 'مكتمل' : sale.status === 'pending' ? 'معلق' : 'ملغي'
    ])
  }

  const exportOptions: ExportOptions = {
    includeTimestamp: true,
    includeCompanyInfo: true,
    customFooter: `إجمالي المبيعات: ${sales.length} | إجمالي القيمة: ${sales.reduce((sum, sale) => sum + sale.total, 0).toLocaleString()} ر.ي`,
    ...options
  }

  switch (format) {
    case 'excel':
      exportToExcel(data, exportOptions)
      break
    case 'json':
      exportToJSON(sales, data.filename, exportOptions)
      break
    default:
      exportToCSV(data, exportOptions)
  }
}

// تصدير تقرير المنتجات
export function exportProductsReport(products: any[]) {
  const data: ExportData = {
    title: 'تقرير المنتجات',
    filename: `products-report-${new Date().toISOString().split('T')[0]}`,
    headers: [
      'اسم المنتج',
      'الوصف',
      'الفئة',
      'السعر',
      'التكلفة',
      'الوحدة',
      'المخزون الحالي',
      'الحد الأدنى',
      'المورد',
      'الموقع',
      'الحالة'
    ],
    rows: products.map(product => [
      product.name,
      product.description || '',
      product.category,
      product.price,
      product.cost,
      product.unit,
      product.stock,
      product.minStock,
      product.supplier || '',
      product.location || '',
      product.stock === 0 ? 'نفد المخزون' : 
      product.stock <= product.minStock ? 'مخزون منخفض' : 'متوفر'
    ])
  }
  
  exportToCSV(data)
}

// تصدير تقرير العملاء
export function exportCustomersReport(customers: any[]) {
  const data: ExportData = {
    title: 'تقرير العملاء',
    filename: `customers-report-${new Date().toISOString().split('T')[0]}`,
    headers: [
      'اسم العميل',
      'الشركة',
      'البريد الإلكتروني',
      'الهاتف',
      'العنوان',
      'النوع',
      'الحالة',
      'حد الائتمان',
      'الدين الحالي',
      'إجمالي المشتريات',
      'آخر شراء'
    ],
    rows: customers.map(customer => [
      customer.name,
      customer.company || '',
      customer.email || '',
      customer.phone,
      customer.address || '',
      customer.type === 'individual' ? 'فرد' : 'شركة',
      customer.status === 'active' ? 'نشط' : 'غير نشط',
      customer.creditLimit,
      customer.currentDebt,
      customer.totalPurchases,
      customer.lastPurchase ? new Date(customer.lastPurchase).toLocaleDateString('ar-SA') : 'لا يوجد'
    ])
  }
  
  exportToCSV(data)
}

// تصدير تقرير المخزون
export function exportInventoryReport(products: any[]) {
  const data: ExportData = {
    title: 'تقرير المخزون',
    filename: `inventory-report-${new Date().toISOString().split('T')[0]}`,
    headers: [
      'اسم المنتج',
      'الفئة',
      'المخزون الحالي',
      'الحد الأدنى',
      'التكلفة الوحدة',
      'قيمة المخزون',
      'حالة المخزون'
    ],
    rows: products.map(product => [
      product.name,
      product.category,
      product.stock,
      product.minStock,
      product.cost,
      product.stock * product.cost,
      product.stock === 0 ? 'نفد المخزون' : 
      product.stock <= product.minStock ? 'مخزون منخفض' : 'متوفر'
    ])
  }
  
  exportToCSV(data)
}

// تصدير تقرير مالي شامل
export function exportFinancialReport(data: {
  sales: any[]
  products: any[]
  period: string
  stats: any
}) {
  const reportData = {
    title: `التقرير المالي - ${data.period}`,
    filename: `financial-report-${data.period}-${new Date().toISOString().split('T')[0]}`,
    summary: {
      totalRevenue: data.stats.totalRevenue,
      totalCost: data.stats.totalCost,
      totalProfit: data.stats.totalProfit,
      profitMargin: data.stats.profitMargin,
      totalOrders: data.stats.totalOrders,
      averageOrderValue: data.stats.averageOrderValue,
      inventoryValue: data.stats.inventoryValue
    },
    sales: data.sales,
    generatedAt: new Date().toISOString()
  }
  
  exportToJSON(reportData, reportData.filename)
}

// طباعة التقرير
export function printReport(elementId: string, title: string) {
  const printContent = document.getElementById(elementId)
  if (!printContent) return
  
  const printWindow = window.open('', '_blank')
  if (!printWindow) return
  
  printWindow.document.write(`
    <!DOCTYPE html>
    <html dir="rtl">
    <head>
      <title>${title}</title>
      <meta charset="utf-8">
      <style>
        body { 
          font-family: Arial, sans-serif; 
          margin: 20px;
          direction: rtl;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #333;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .header h1 {
          margin: 0;
          color: #333;
        }
        .header p {
          margin: 5px 0;
          color: #666;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: right;
        }
        th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        .summary {
          background-color: #f9f9f9;
          padding: 15px;
          border-radius: 5px;
          margin: 20px 0;
        }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>${title}</h1>
        <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
        <p>وقت الطباعة: ${new Date().toLocaleTimeString('ar-SA')}</p>
      </div>
      ${printContent.innerHTML}
    </body>
    </html>
  `)
  
  printWindow.document.close()
  printWindow.focus()
  
  setTimeout(() => {
    printWindow.print()
    printWindow.close()
  }, 250)
}
