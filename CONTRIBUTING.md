# دليل المساهمة | Contributing Guide

نرحب بمساهماتكم في تطوير نظام المحاسبة الحديث! هذا الدليل سيساعدكم على البدء.

## 🚀 البدء السريع | Quick Start

### المتطلبات الأساسية | Prerequisites
- Node.js 18 أو أحدث
- npm أو yarn أو pnpm
- Git
- PostgreSQL (اختياري للتطوير المحلي)

### إعداد البيئة المحلية | Local Development Setup

1. **Fork المشروع**
   ```bash
   # انقر على زر Fork في GitHub
   ```

2. **استنساخ المشروع**
   ```bash
   git clone https://github.com/your-username/modern-accounting-system.git
   cd modern-accounting-system
   ```

3. **تثبيت التبعيات**
   ```bash
   npm install
   ```

4. **إعداد متغيرات البيئة**
   ```bash
   cp .env.example .env.local
   # قم بتعديل .env.local وإضافة القيم المطلوبة
   ```

5. **تشغيل الخادم التطويري**
   ```bash
   npm run dev
   ```

## 📝 أنواع المساهمات | Types of Contributions

### 🐛 الإبلاغ عن الأخطاء | Bug Reports
- استخدم GitHub Issues للإبلاغ عن الأخطاء
- قدم وصفاً واضحاً للمشكلة
- أضف خطوات إعادة إنتاج المشكلة
- أرفق لقطات شاشة إذا أمكن

### ✨ طلب ميزات جديدة | Feature Requests
- استخدم GitHub Issues لطلب ميزات جديدة
- اشرح الحاجة للميزة والفائدة المتوقعة
- قدم أمثلة أو مراجع إذا أمكن

### 🔧 تحسين الكود | Code Improvements
- إصلاح الأخطاء
- تحسين الأداء
- إضافة ميزات جديدة
- تحسين التوثيق

## 🛠️ عملية التطوير | Development Process

### 1. إنشاء فرع جديد | Create a Branch
```bash
git checkout -b feature/amazing-feature
# أو
git checkout -b fix/bug-description
```

### 2. تطوير الميزة | Develop the Feature
- اتبع معايير الكود المحددة
- أضف اختبارات للكود الجديد
- تأكد من أن جميع الاختبارات تمر
- حدث التوثيق إذا لزم الأمر

### 3. اختبار التغييرات | Test Changes
```bash
npm run lint          # فحص الكود
npm run type-check     # فحص الأنواع
npm run build          # بناء المشروع
```

### 4. Commit التغييرات | Commit Changes
```bash
git add .
git commit -m "feat: add amazing feature"
```

### 5. Push ورفع Pull Request | Push and Create PR
```bash
git push origin feature/amazing-feature
```
ثم افتح Pull Request في GitHub.

## 📋 معايير الكود | Code Standards

### TypeScript
- استخدم TypeScript لجميع الملفات
- تجنب استخدام `any` قدر الإمكان
- أضف أنواع واضحة للمتغيرات والدوال

### React Components
- استخدم Functional Components مع Hooks
- اتبع نمط تسمية PascalCase للمكونات
- أضف PropTypes أو TypeScript interfaces

### Styling
- استخدم Tailwind CSS للتصميم
- اتبع نظام التصميم الموحد
- تأكد من دعم RTL للغة العربية

### API Routes (tRPC)
- استخدم Zod للتحقق من صحة البيانات
- أضف معالجة الأخطاء المناسبة
- وثق الـ API endpoints

## 🧪 الاختبارات | Testing

### تشغيل الاختبارات | Running Tests
```bash
npm run test           # تشغيل جميع الاختبارات
npm run test:watch     # تشغيل الاختبارات في وضع المراقبة
npm run test:coverage  # تقرير التغطية
```

### كتابة الاختبارات | Writing Tests
- أضف اختبارات للميزات الجديدة
- اختبر الحالات الحدية
- استخدم أسماء وصفية للاختبارات

## 📚 التوثيق | Documentation

### تحديث التوثيق | Updating Documentation
- حدث README.md للميزات الجديدة
- أضف تعليقات في الكود
- حدث CHANGELOG.md

### كتابة التعليقات | Writing Comments
- استخدم اللغة العربية للتعليقات
- اشرح المنطق المعقد
- أضف أمثلة للاستخدام

## 🎯 إرشادات Pull Request | PR Guidelines

### قبل إرسال PR | Before Submitting PR
- [ ] تأكد من أن الكود يعمل محلياً
- [ ] أضف اختبارات للتغييرات
- [ ] حدث التوثيق
- [ ] تأكد من مرور جميع الاختبارات
- [ ] اتبع معايير الكود

### وصف PR | PR Description
- اشرح ما تم تغييره ولماذا
- أرفق لقطات شاشة للتغييرات البصرية
- اذكر أي مشاكل مرتبطة (closes #123)
- أضف قائمة مراجعة إذا لزم الأمر

## 🏷️ نظام التسمية | Naming Conventions

### Commit Messages
استخدم نظام Conventional Commits:
```
feat: إضافة ميزة جديدة
fix: إصلاح خطأ
docs: تحديث التوثيق
style: تحسين التصميم
refactor: إعادة هيكلة الكود
test: إضافة اختبارات
chore: مهام صيانة
```

### Branch Names
```
feature/feature-name
fix/bug-description
docs/documentation-update
refactor/code-improvement
```

## 🤝 قواعد السلوك | Code of Conduct

### كن محترماً | Be Respectful
- احترم جميع المساهمين
- كن صبوراً مع المبتدئين
- قدم ملاحظات بناءة

### كن مفيداً | Be Helpful
- ساعد الآخرين في حل المشاكل
- شارك المعرفة والخبرات
- اقترح تحسينات

## 📞 التواصل | Communication

### القنوات المتاحة | Available Channels
- GitHub Issues - للأخطاء وطلب الميزات
- GitHub Discussions - للنقاشات العامة
- Pull Request Comments - لمراجعة الكود

### اللغة | Language
- يمكن استخدام العربية أو الإنجليزية
- استخدم لغة واضحة ومهذبة
- اشرح الأفكار بالتفصيل

## 🎉 شكر وتقدير | Recognition

جميع المساهمين سيتم ذكرهم في:
- ملف CONTRIBUTORS.md
- صفحة الشكر في التطبيق
- ملاحظات الإصدار

---

**شكراً لمساهمتكم في تطوير نظام المحاسبة الحديث! 🙏**

للأسئلة أو المساعدة، لا تترددوا في فتح Issue أو التواصل معنا.
