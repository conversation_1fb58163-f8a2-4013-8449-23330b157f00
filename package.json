{"name": "modern-accounting-system", "version": "1.0.0", "description": "نظام محاسبة حديث باستخدام أحدث التقنيات", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate:sqlite", "db:migrate": "node scripts/migrate.js", "db:studio": "drizzle-kit studio", "db:seed": "node scripts/seed-database.js", "db:setup": "node scripts/seed-database.js", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@clerk/nextjs": "^5.0.0", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.11.0", "@trpc/client": "^11.4.3", "@trpc/next": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "better-sqlite3": "^11.6.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.0", "date-fns": "^3.0.0", "drizzle-orm": "^0.29.0", "firebase": "^10.7.1", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "lucide-react": "^0.309.0", "next": "^15.0.0", "next-intl": "^3.4.0", "quagga": "^0.12.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-to-print": "^2.15.1", "recharts": "^2.10.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/better-sqlite3": "^7.6.11", "@types/jest": "^29.5.12", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "drizzle-kit": "^0.20.0", "eslint": "^8.56.0", "eslint-config-next": "^15.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.0"}}