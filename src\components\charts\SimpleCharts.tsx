'use client'

import { useState, useEffect } from 'react'

interface ChartProps {
  data: any[]
  height?: number
  title?: string
}

// رسم بياني بسيط للمبيعات اليومية
export function SimpleDailySalesChart({ data, height = 300 }: ChartProps) {
  const maxValue = Math.max(...data.map(item => item.total))
  
  return (
    <div style={{ height: `${height}px` }} className="p-4">
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 dark:text-gray-100">المبيعات اليومية</h4>
        <div className="space-y-3">
          {data.slice(0, 7).map((item, index) => {
            const percentage = maxValue > 0 ? (item.total / maxValue) * 100 : 0
            const date = new Date(item.date).toLocaleDateString('ar-SA')
            
            return (
              <div key={index} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">{date}</span>
                  <span className="font-medium">{item.total.toLocaleString()} ر.س</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                  <div
                    className="bg-blue-500 h-3 rounded-full transition-all duration-500"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

// رسم بياني بسيط للمنتجات الأكثر مبيعاً
export function SimpleTopProductsChart({ data, height = 300 }: ChartProps) {
  const maxValue = Math.max(...data.map(item => item.quantity))
  
  return (
    <div style={{ height: `${height}px` }} className="p-4">
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 dark:text-gray-100">المنتجات الأكثر مبيعاً</h4>
        <div className="space-y-3">
          {data.slice(0, 5).map((item, index) => {
            const percentage = maxValue > 0 ? (item.quantity / maxValue) * 100 : 0
            const colors = [
              'bg-blue-500',
              'bg-green-500', 
              'bg-yellow-500',
              'bg-red-500',
              'bg-purple-500'
            ]
            
            return (
              <div key={index} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400 truncate">{item.name}</span>
                  <span className="font-medium">{item.quantity}</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                  <div
                    className={`${colors[index % colors.length]} h-3 rounded-full transition-all duration-500`}
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

// رسم دائري بسيط لتوزيع المبيعات حسب الفئة
export function SimpleCategorySalesChart({ data, height = 300 }: ChartProps) {
  const total = data.reduce((sum, item) => sum + item.total, 0)
  const colors = [
    'bg-blue-500',
    'bg-green-500',
    'bg-yellow-500', 
    'bg-red-500',
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500'
  ]
  
  return (
    <div style={{ height: `${height}px` }} className="p-4">
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 dark:text-gray-100">توزيع المبيعات حسب الفئة</h4>
        <div className="space-y-3">
          {data.map((item, index) => {
            const percentage = total > 0 ? (item.total / total) * 100 : 0
            
            return (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full ${colors[index % colors.length]}`} />
                  <span className="text-sm text-gray-600 dark:text-gray-400">{item.category}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{item.total.toLocaleString()} ر.س</div>
                  <div className="text-xs text-gray-500">{percentage.toFixed(1)}%</div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

// رسم بياني بسيط للأرباح الشهرية
export function SimpleMonthlyProfitChart({ data, height = 300 }: ChartProps) {
  const maxValue = Math.max(...data.map(item => Math.max(item.revenue, item.expenses, item.profit)))
  
  return (
    <div style={{ height: `${height}px` }} className="p-4">
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 dark:text-gray-100">الأرباح الشهرية</h4>
        <div className="space-y-4">
          {data.slice(0, 6).map((item, index) => {
            const revenuePercentage = maxValue > 0 ? (item.revenue / maxValue) * 100 : 0
            const expensesPercentage = maxValue > 0 ? (item.expenses / maxValue) * 100 : 0
            const profitPercentage = maxValue > 0 ? (item.profit / maxValue) * 100 : 0
            
            return (
              <div key={index} className="space-y-2">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">{item.month}</div>
                
                {/* الإيرادات */}
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span className="text-green-600">الإيرادات</span>
                    <span>{item.revenue.toLocaleString()} ر.س</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${revenuePercentage}%` }}
                    />
                  </div>
                </div>
                
                {/* المصروفات */}
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span className="text-red-600">المصروفات</span>
                    <span>{item.expenses.toLocaleString()} ر.س</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-red-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${expensesPercentage}%` }}
                    />
                  </div>
                </div>
                
                {/* صافي الربح */}
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span className="text-blue-600">صافي الربح</span>
                    <span>{item.profit.toLocaleString()} ر.س</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${profitPercentage}%` }}
                    />
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

// مكون رسم بياني عام مع تأثيرات بصرية
export function AnimatedChart({ children, className = "" }: { children: React.ReactNode, className?: string }) {
  const [isVisible, setIsVisible] = useState(false)
  
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100)
    return () => clearTimeout(timer)
  }, [])
  
  return (
    <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'} ${className}`}>
      {children}
    </div>
  )
}
