'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  DollarSign, Users, Package, ShoppingCart, 
  TrendingUp, TrendingDown, AlertTriangle, 
  Calendar, Eye, ArrowRight, RefreshCw,
  BarChart3, PieChart, Activity
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { realDataManager } from '@/lib/real-storage'
// import InventoryAlerts from '@/components/alerts/InventoryAlerts'
// import { SimpleDailySalesChart, SimpleTopProductsChart, SimpleCategorySalesChart, SimpleMonthlyProfitChart, AnimatedChart } from '@/components/charts/SimpleCharts'
import Link from 'next/link'

export default function DashboardPage() {
  const [dashboardData, setDashboardData] = useState<any>({
    sales: [],
    customers: [],
    products: [],
    returns: [],
    suppliers: []
  })
  const [loading, setLoading] = useState(true)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    loadDashboardData()
  }, [])

  const loadDashboardData = () => {
    setLoading(true)
    try {
      const sales = realDataManager.getSales()
      const customers = realDataManager.getCustomers()
      const products = realDataManager.getProducts()
      const returns = realDataManager.getReturns()
      const suppliers = realDataManager.getSuppliers()

      setDashboardData({
        sales,
        customers,
        products,
        returns,
        suppliers
      })
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  // حساب الإحصائيات
  const stats = {
    // إحصائيات المبيعات
    totalSales: dashboardData.sales.reduce((sum: number, sale: any) => sum + sale.total, 0),
    todaySales: dashboardData.sales.filter((sale: any) => {
      const today = new Date().toDateString()
      const saleDate = new Date(sale.createdAt).toDateString()
      return today === saleDate
    }).reduce((sum: number, sale: any) => sum + sale.total, 0),
    totalOrders: dashboardData.sales.length,
    completedOrders: dashboardData.sales.filter((sale: any) => sale.status === 'completed').length,
    
    // إحصائيات العملاء
    totalCustomers: dashboardData.customers.length,
    activeCustomers: dashboardData.customers.filter((customer: any) => customer.status === 'active').length,
    totalDebt: dashboardData.customers.reduce((sum: number, customer: any) => sum + (customer.debt || 0), 0),
    
    // إحصائيات المنتجات
    totalProducts: dashboardData.products.length,
    lowStockProducts: dashboardData.products.filter((product: any) => product.stock <= product.minStock).length,
    outOfStockProducts: dashboardData.products.filter((product: any) => product.stock === 0).length,
    totalInventoryValue: dashboardData.products.reduce((sum: number, product: any) => sum + (product.price * product.stock), 0),
    
    // إحصائيات المرتجعات
    totalReturns: dashboardData.returns.length,
    totalReturnValue: dashboardData.returns.reduce((sum: number, ret: any) => sum + ret.totalAmount, 0),
    
    // إحصائيات الموردين
    totalSuppliers: dashboardData.suppliers.length,
    activeSuppliers: dashboardData.suppliers.filter((supplier: any) => supplier.status === 'نشط').length
  }

  // أفضل المنتجات مبيعاً
  const topProducts = dashboardData.sales
    .flatMap((sale: any) => sale.items)
    .reduce((acc: any, item: any) => {
      const existing = acc.find((p: any) => p.productName === item.productName)
      if (existing) {
        existing.totalQuantity += item.quantity
        existing.totalValue += item.unitPrice * item.quantity
      } else {
        acc.push({
          productName: item.productName,
          totalQuantity: item.quantity,
          totalValue: item.unitPrice * item.quantity
        })
      }
      return acc
    }, [])
    .sort((a: any, b: any) => b.totalValue - a.totalValue)
    .slice(0, 5)

  // أفضل العملاء
  const topCustomers = dashboardData.sales
    .reduce((acc: any, sale: any) => {
      const existing = acc.find((c: any) => c.customerName === sale.customerName)
      if (existing) {
        existing.totalOrders += 1
        existing.totalValue += sale.total
      } else {
        acc.push({
          customerName: sale.customerName,
          totalOrders: 1,
          totalValue: sale.total
        })
      }
      return acc
    }, [])
    .sort((a: any, b: any) => b.totalValue - a.totalValue)
    .slice(0, 5)

  // آخر المبيعات
  const recentSales = dashboardData.sales
    .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5)

  if (!mounted || loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
          <p className="text-gray-600 mt-2">نظرة عامة على أداء النشاط التجاري</p>
        </div>
        <Button onClick={loadDashboardData} variant="outline">
          <RefreshCw className="h-4 w-4 ml-2" />
          تحديث البيانات
        </Button>
      </div>

      {/* Main Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المبيعات</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalSales)}</div>
            <p className="text-xs text-muted-foreground">
              مبيعات اليوم: {formatCurrency(stats.todaySales)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الطلبات</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              مكتملة: {stats.completedOrders}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">العملاء</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCustomers}</div>
            <p className="text-xs text-muted-foreground">
              نشط: {stats.activeCustomers}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المنتجات</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              مخزون منخفض: {stats.lowStockProducts}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">المخزون</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>قيمة المخزون الإجمالية</span>
              <span className="font-bold">{formatCurrency(stats.totalInventoryValue)}</span>
            </div>
            <div className="flex justify-between">
              <span>منتجات نفد مخزونها</span>
              <Badge variant={stats.outOfStockProducts > 0 ? "destructive" : "secondary"}>
                {stats.outOfStockProducts}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>منتجات مخزون منخفض</span>
              <Badge variant={stats.lowStockProducts > 0 ? "outline" : "secondary"}>
                {stats.lowStockProducts}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">الديون والمرتجعات</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>إجمالي الديون</span>
              <span className="font-bold text-red-600">{formatCurrency(stats.totalDebt)}</span>
            </div>
            <div className="flex justify-between">
              <span>عدد المرتجعات</span>
              <span className="font-bold">{stats.totalReturns}</span>
            </div>
            <div className="flex justify-between">
              <span>قيمة المرتجعات</span>
              <span className="font-bold text-red-600">{formatCurrency(stats.totalReturnValue)}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">الموردين</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>إجمالي الموردين</span>
              <span className="font-bold">{stats.totalSuppliers}</span>
            </div>
            <div className="flex justify-between">
              <span>موردين نشطين</span>
              <span className="font-bold text-green-600">{stats.activeSuppliers}</span>
            </div>
            <div className="flex justify-between">
              <span>معدل النشاط</span>
              <span className="font-bold">
                {stats.totalSuppliers > 0 ? Math.round((stats.activeSuppliers / stats.totalSuppliers) * 100) : 0}%
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Lists */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Products */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              أفضل المنتجات مبيعاً
            </CardTitle>
          </CardHeader>
          <CardContent>
            {topProducts.length === 0 ? (
              <p className="text-center text-gray-500 py-4">لا توجد بيانات مبيعات</p>
            ) : (
              <div className="space-y-3">
                {topProducts.map((product: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{product.productName}</p>
                      <p className="text-sm text-gray-600">الكمية: {product.totalQuantity}</p>
                    </div>
                    <div className="text-left">
                      <p className="font-bold text-green-600">{formatCurrency(product.totalValue)}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Top Customers */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              أفضل العملاء
            </CardTitle>
          </CardHeader>
          <CardContent>
            {topCustomers.length === 0 ? (
              <p className="text-center text-gray-500 py-4">لا توجد بيانات عملاء</p>
            ) : (
              <div className="space-y-3">
                {topCustomers.map((customer: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{customer.customerName}</p>
                      <p className="text-sm text-gray-600">الطلبات: {customer.totalOrders}</p>
                    </div>
                    <div className="text-left">
                      <p className="font-bold text-blue-600">{formatCurrency(customer.totalValue)}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Sales */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            آخر المبيعات
          </CardTitle>
          <Link href="/pos">
            <Button variant="outline" size="sm">
              <ArrowRight className="h-4 w-4 ml-2" />
              عرض الكل
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          {recentSales.length === 0 ? (
            <p className="text-center text-gray-500 py-8">لا توجد مبيعات حديثة</p>
          ) : (
            <div className="space-y-3">
              {recentSales.map((sale: any) => (
                <div key={sale.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-4">
                      <div>
                        <p className="font-medium">فاتورة #{sale.id}</p>
                        <p className="text-sm text-gray-600">{sale.customerName}</p>
                      </div>
                      <Badge className={
                        sale.status === 'completed' ? 'bg-green-100 text-green-800' :
                        sale.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }>
                        {sale.status === 'completed' ? 'مكتملة' : sale.status === 'pending' ? 'معلقة' : 'ملغية'}
                      </Badge>
                    </div>
                  </div>
                  <div className="text-left">
                    <p className="font-bold">{formatCurrency(sale.total)}</p>
                    <p className="text-sm text-gray-600">
                      {new Date(sale.createdAt).toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>إجراءات سريعة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/pos">
              <Button className="w-full h-20 flex flex-col gap-2">
                <ShoppingCart className="h-6 w-6" />
                نقطة البيع
              </Button>
            </Link>
            <Link href="/products">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <Package className="h-6 w-6" />
                إدارة المنتجات
              </Button>
            </Link>
            <Link href="/customers">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <Users className="h-6 w-6" />
                إدارة العملاء
              </Button>
            </Link>
            <Link href="/reports">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <BarChart3 className="h-6 w-6" />
                التقارير
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* إحصائيات سريعة */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              المبيعات اليومية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">اليوم</span>
                <span className="font-bold text-green-600">25,000 ر.س</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">أمس</span>
                <span className="font-bold">19,000 ر.س</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">هذا الأسبوع</span>
                <span className="font-bold">142,000 ر.س</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">هذا الشهر</span>
                <span className="font-bold">580,000 ر.س</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              المنتجات الأكثر مبيعاً
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">حديد تسليح 12مم</span>
                <span className="font-bold">150 طن</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">أسمنت بورتلاند</span>
                <span className="font-bold">120 كيس</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">رمل مغسول</span>
                <span className="font-bold">100 م³</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">حصى مدرج</span>
                <span className="font-bold">80 م³</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              توزيع المبيعات حسب الفئة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">حديد ومعادن</span>
                </div>
                <span className="font-bold">45,000 ر.س</span>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">أسمنت ومواد</span>
                </div>
                <span className="font-bold">35,000 ر.س</span>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm">رمل وحصى</span>
                </div>
                <span className="font-bold">25,000 ر.س</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              الأرباح الشهرية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">الإيرادات</span>
                <span className="font-bold text-green-600">250,000 ر.س</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">المصروفات</span>
                <span className="font-bold text-red-600">160,000 ر.س</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">صافي الربح</span>
                <span className="font-bold text-blue-600">90,000 ر.س</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">هامش الربح</span>
                <span className="font-bold">36%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* تنبيهات المخزون */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              تنبيهات المخزون
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <div>
                    <p className="font-medium text-red-900">نفد مخزون أسمنت بورتلاند</p>
                    <p className="text-sm text-red-700">يجب إعادة التموين فوراً</p>
                  </div>
                </div>
                <Badge variant="destructive">نفد</Badge>
              </div>

              <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <div>
                    <p className="font-medium text-yellow-900">مخزون حديد تسليح 12مم منخفض</p>
                    <p className="text-sm text-yellow-700">متبقي 5 طن فقط</p>
                  </div>
                </div>
                <Badge variant="secondary">منخفض</Badge>
              </div>

              <div className="flex items-center justify-between p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="h-4 w-4 text-orange-500" />
                  <div>
                    <p className="font-medium text-orange-900">مخزون رمل مغسول منخفض</p>
                    <p className="text-sm text-orange-700">متبقي 15 متر مكعب</p>
                  </div>
                </div>
                <Badge variant="secondary">منخفض</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
