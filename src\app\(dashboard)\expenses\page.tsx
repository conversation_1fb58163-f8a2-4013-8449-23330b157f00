'use client'

import { useState, useEffect } from 'react'
import { Plus, Search, Filter, Download, Eye, Edit, Trash2, Receipt, TrendingDown, Calendar, Tag, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { formatCurrency, formatDate } from '@/lib/utils'
import { realDataManager } from '@/lib/real-storage'

// Mock data for expenses (will be replaced with real data)
const mockExpenses = [
  {
    id: 'EXP-001',
    description: 'إيجار المكتب - يناير 2024',
    amount: 2000000, // 8,000 ر.س × 250
    date: '2024-01-01',
    category: 'rent',
    vendor: 'شركة العقارات المتميزة',
    paymentMethod: 'bank_transfer',
    status: 'paid',
    receiptNumber: 'REC-001',
    notes: 'إيجار شهري'
  },
  {
    id: 'EXP-002',
    description: 'فواتير الكهرباء والماء',
    amount: 300000, // 1,200 ر.س × 250
    date: '2024-01-05',
    category: 'utilities',
    vendor: 'شركة الكهرباء والماء',
    paymentMethod: 'credit_card',
    status: 'paid',
    receiptNumber: 'REC-002',
    notes: ''
  },
  {
    id: 'EXP-003',
    description: 'مستلزمات مكتبية',
    amount: 112500, // 450 ر.س × 250
    date: '2024-01-08',
    category: 'office_supplies',
    vendor: 'مكتبة الأعمال',
    paymentMethod: 'cash',
    status: 'paid',
    receiptNumber: 'REC-003',
    notes: 'أوراق وأقلام ومستلزمات'
  },
  {
    id: 'EXP-004',
    description: 'صيانة أجهزة الكمبيوتر',
    amount: 625000, // 2,500 ر.س × 250
    date: '2024-01-10',
    category: 'maintenance',
    vendor: 'مركز الصيانة التقني',
    paymentMethod: 'bank_transfer',
    status: 'pending',
    receiptNumber: 'REC-004',
    notes: 'صيانة دورية للأجهزة'
  },
  {
    id: 'EXP-005',
    description: 'اشتراك البرامج الشهري',
    amount: 200000, // 800 ر.س × 250
    date: '2024-01-12',
    category: 'software',
    vendor: 'شركة البرمجيات المتقدمة',
    paymentMethod: 'credit_card',
    status: 'paid',
    receiptNumber: 'REC-005',
    notes: 'اشتراك شهري في البرامج'
  },
  {
    id: 'EXP-006',
    description: 'وقود السيارات',
    amount: 150000, // 600 ر.س × 250
    date: '2024-01-15',
    category: 'transportation',
    vendor: 'محطة الوقود الرئيسية',
    paymentMethod: 'cash',
    status: 'paid',
    receiptNumber: 'REC-006',
    notes: 'وقود سيارات الشركة'
  }
]

const expenseCategories = {
  rent: 'إيجار',
  utilities: 'مرافق',
  office_supplies: 'مستلزمات مكتبية',
  maintenance: 'صيانة',
  software: 'برمجيات',
  transportation: 'مواصلات',
  marketing: 'تسويق',
  other: 'أخرى'
}

const paymentMethods = {
  bank_transfer: 'تحويل بنكي',
  credit_card: 'بطاقة ائتمان',
  cash: 'نقداً',
  check: 'شيك'
}

const expenseStatuses = {
  paid: { label: 'مدفوع', color: 'bg-green-100 text-green-800' },
  pending: { label: 'في الانتظار', color: 'bg-yellow-100 text-yellow-800' },
  overdue: { label: 'متأخر', color: 'bg-red-100 text-red-800' }
}

export default function ExpensesPage() {
  const [expenses, setExpenses] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    // For now, use mock data. In the future, this can be connected to real data
    setExpenses(mockExpenses)
  }, [])

  // Expense statistics
  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)
  const paidExpenses = expenses.filter(e => e.status === 'paid').reduce((sum, expense) => sum + expense.amount, 0)
  const pendingExpenses = expenses.filter(e => e.status === 'pending').reduce((sum, expense) => sum + expense.amount, 0)
  const overdueExpenses = expenses.filter(e => e.status === 'overdue').reduce((sum, expense) => sum + expense.amount, 0)

  // Category breakdown
  const categoryBreakdown = Object.entries(expenseCategories).map(([key, label]) => {
    const categoryExpenses = expenses.filter(e => e.category === key)
    const total = categoryExpenses.reduce((sum, expense) => sum + expense.amount, 0)
    return { category: key, label, total, count: categoryExpenses.length }
  }).filter(item => item.total > 0)

  // Filter expenses
  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.vendor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === 'all' || expense.category === categoryFilter
    const matchesStatus = statusFilter === 'all' || expense.status === statusFilter
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  const handleAddExpense = () => {
    // هنا سيتم إضافة منطق إضافة مصروف جديد
    setIsAddDialogOpen(false)
  }

  if (!mounted) {
    return (
      <div className="p-6 flex items-center justify-center h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">المصروفات</h1>
            <p className="text-gray-600 mt-2">إدارة ومتابعة جميع مصروفات الشركة</p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 ml-2" />
                إضافة مصروف جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة مصروف جديد</DialogTitle>
                <DialogDescription>
                  أدخل تفاصيل المصروف الجديد
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="description">وصف المصروف</Label>
                  <Input id="description" placeholder="وصف المصروف" />
                </div>
                <div>
                  <Label htmlFor="amount">المبلغ</Label>
                  <Input id="amount" type="number" placeholder="0.00" />
                </div>
                <div>
                  <Label htmlFor="category">الفئة</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الفئة" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(expenseCategories).map(([key, label]) => (
                        <SelectItem key={key} value={key}>{label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="vendor">المورد</Label>
                  <Input id="vendor" placeholder="اسم المورد" />
                </div>
                <div>
                  <Label htmlFor="paymentMethod">طريقة الدفع</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر طريقة الدفع" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(paymentMethods).map(([key, label]) => (
                        <SelectItem key={key} value={key}>{label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="receiptNumber">رقم الإيصال</Label>
                  <Input id="receiptNumber" placeholder="رقم الإيصال" />
                </div>
                <div>
                  <Label htmlFor="notes">ملاحظات</Label>
                  <Textarea id="notes" placeholder="ملاحظات إضافية (اختياري)" />
                </div>
                <Button onClick={handleAddExpense} className="w-full">
                  إضافة المصروف
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المصروفات</CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{formatCurrency(totalExpenses)}</div>
              <p className="text-xs text-muted-foreground">
                {expenses.length} مصروف
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المصروفات المدفوعة</CardTitle>
              <Receipt className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{formatCurrency(paidExpenses)}</div>
              <p className="text-xs text-muted-foreground">
                {expenses.filter(e => e.status === 'paid').length} مصروف
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المصروفات المعلقة</CardTitle>
              <Calendar className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{formatCurrency(pendingExpenses)}</div>
              <p className="text-xs text-muted-foreground">
                {expenses.filter(e => e.status === 'pending').length} مصروف
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">أكبر فئة مصروفات</CardTitle>
              <Tag className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {categoryBreakdown.length > 0 ? 
                  categoryBreakdown.sort((a, b) => b.total - a.total)[0].label : 
                  'لا توجد بيانات'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                {categoryBreakdown.length > 0 ? 
                  formatCurrency(categoryBreakdown.sort((a, b) => b.total - a.total)[0].total) : 
                  ''
                }
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Category Breakdown */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>توزيع المصروفات حسب الفئة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {categoryBreakdown.map((item) => (
                <div key={item.category} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">{item.label}</span>
                    <Badge variant="secondary">{item.count}</Badge>
                  </div>
                  <div className="text-lg font-bold text-red-600">{formatCurrency(item.total)}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>البحث والتصفية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="البحث في المصروفات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="الفئة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  {Object.entries(expenseCategories).map(([key, label]) => (
                    <SelectItem key={key} value={key}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="paid">مدفوع</SelectItem>
                  <SelectItem value="pending">في الانتظار</SelectItem>
                  <SelectItem value="overdue">متأخر</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline">
                <Download className="h-4 w-4 ml-2" />
                تصدير
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Expenses Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المصروفات</CardTitle>
            <CardDescription>
              عرض جميع المصروفات مع إمكانية البحث والتصفية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم المصروف</TableHead>
                  <TableHead>الوصف</TableHead>
                  <TableHead>المبلغ</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>الفئة</TableHead>
                  <TableHead>المورد</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredExpenses.map((expense) => (
                  <TableRow key={expense.id}>
                    <TableCell className="font-medium">{expense.id}</TableCell>
                    <TableCell>{expense.description}</TableCell>
                    <TableCell className="font-semibold text-red-600">{formatCurrency(expense.amount)}</TableCell>
                    <TableCell>{formatDate(expense.date)}</TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {expenseCategories[expense.category as keyof typeof expenseCategories]}
                      </Badge>
                    </TableCell>
                    <TableCell>{expense.vendor}</TableCell>
                    <TableCell>
                      <Badge className={expenseStatuses[expense.status as keyof typeof expenseStatuses].color}>
                        {expenseStatuses[expense.status as keyof typeof expenseStatuses].label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
