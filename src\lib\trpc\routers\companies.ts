import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, publicProcedure } from '../init'

// Mock data for companies
const mockCompanies = [
  {
    id: '1',
    name: 'شركة التقنية المتقدمة',
    email: '<EMAIL>',
    phone: '+966501234567',
    address: 'الرياض، المملكة العربية السعودية',
    taxNumber: '*********',
    logo: '',
    userId: 'user_1',
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

const createCompanySchema = z.object({
  name: z.string().min(1, 'اسم الشركة مطلوب'),
  email: z.string().email('البريد الإلكتروني غير صحيح').optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  taxNumber: z.string().optional(),
  logo: z.string().optional(),
})

const updateCompanySchema = createCompanySchema.partial().extend({
  id: z.string(),
})

export const companiesRouter = createTRPCRouter({
  // إنشاء شركة جديدة
  create: publicProcedure
    .input(createCompanySchema)
    .mutation(async ({ input }) => {
      const newCompany = {
        id: String(mockCompanies.length + 1),
        ...input,
        userId: 'user_1',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockCompanies.push(newCompany);
      return newCompany;
    }),

  // الحصول على شركات المستخدم
  getAll: publicProcedure.query(async () => {
    return mockCompanies;
  }),

  // الحصول على شركة واحدة
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const company = mockCompanies.find(c => c.id === input.id);
      return company;
    }),

  // تحديث شركة
  update: publicProcedure
    .input(updateCompanySchema)
    .mutation(async ({ input }) => {
      const { id, ...updateData } = input;
      const companyIndex = mockCompanies.findIndex(c => c.id === id);

      if (companyIndex === -1) {
        throw new Error('Company not found');
      }

      mockCompanies[companyIndex] = {
        ...mockCompanies[companyIndex],
        ...updateData,
        updatedAt: new Date(),
      };

      return mockCompanies[companyIndex];
    }),

  // حذف شركة
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      const companyIndex = mockCompanies.findIndex(c => c.id === input.id);

      if (companyIndex === -1) {
        throw new Error('Company not found');
      }

      mockCompanies.splice(companyIndex, 1);
      return { success: true };
    }),
})
