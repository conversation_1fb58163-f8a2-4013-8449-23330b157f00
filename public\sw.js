// Service Worker للنظام المحاسبي
const CACHE_NAME = 'accounting-system-v1'
const urlsToCache = [
  '/',
  '/dashboard',
  '/pos',
  '/products',
  '/customers',
  '/invoices',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json'
]

// تثبيت Service Worker
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...')
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching files')
        return cache.addAll(urlsToCache)
      })
      .then(() => {
        console.log('Service Worker: Installed successfully')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('Service Worker: Installation failed', error)
      })
  )
})

// تفعيل Service Worker
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...')
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      console.log('Service Worker: Activated successfully')
      return self.clients.claim()
    })
  )
})

// اعتراض الطلبات
self.addEventListener('fetch', (event) => {
  // تجاهل الطلبات غير HTTP/HTTPS
  if (!event.request.url.startsWith('http')) {
    return
  }

  // تجاهل طلبات API
  if (event.request.url.includes('/api/')) {
    return
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // إرجاع الملف من الكاش إذا وُجد
        if (response) {
          console.log('Service Worker: Serving from cache', event.request.url)
          return response
        }

        // جلب الملف من الشبكة
        console.log('Service Worker: Fetching from network', event.request.url)
        return fetch(event.request)
          .then((response) => {
            // التحقق من صحة الاستجابة
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response
            }

            // نسخ الاستجابة للكاش
            const responseToCache = response.clone()
            // حفظ في الكاش فقط للـ GET requests
            if (event.request.method === 'GET') {
              caches.open(CACHE_NAME)
                .then((cache) => {
                  cache.put(event.request, responseToCache)
                })
            }

            return response
          })
          .catch((error) => {
            console.error('Service Worker: Fetch failed', error)
            
            // إرجاع صفحة offline إذا كان متاحاً
            if (event.request.destination === 'document') {
              return caches.match('/offline.html')
            }
          })
      })
  )
})

// معالجة الرسائل من التطبيق الرئيسي
self.addEventListener('message', (event) => {
  console.log('Service Worker: Received message', event.data)
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME })
  }
})

// معالجة الإشعارات Push
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received')
  
  const options = {
    body: event.data ? event.data.text() : 'إشعار جديد من النظام المحاسبي',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'عرض التفاصيل',
        icon: '/icons/checkmark.png'
      },
      {
        action: 'close',
        title: 'إغلاق',
        icon: '/icons/xmark.png'
      }
    ],
    tag: 'accounting-notification',
    requireInteraction: true
  }

  event.waitUntil(
    self.registration.showNotification('النظام المحاسبي', options)
  )
})

// معالجة النقر على الإشعارات
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked')
  
  event.notification.close()

  if (event.action === 'explore') {
    // فتح التطبيق
    event.waitUntil(
      clients.openWindow('/dashboard')
    )
  } else if (event.action === 'close') {
    // إغلاق الإشعار فقط
    console.log('Service Worker: Notification dismissed')
  } else {
    // النقر على الإشعار نفسه
    event.waitUntil(
      clients.openWindow('/')
    )
  }
})

// معالجة المزامنة في الخلفية
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered')
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync())
  }
})

// وظيفة المزامنة في الخلفية
async function doBackgroundSync() {
  try {
    console.log('Service Worker: Performing background sync')
    
    // هنا يمكن إضافة منطق مزامنة البيانات
    // مثل رفع البيانات المحفوظة محلياً إلى الخادم
    
    // مثال: مزامنة المبيعات المحفوظة محلياً
    const pendingSales = await getStoredSales()
    if (pendingSales.length > 0) {
      await syncSalesToServer(pendingSales)
    }
    
    console.log('Service Worker: Background sync completed')
  } catch (error) {
    console.error('Service Worker: Background sync failed', error)
  }
}

// وظائف مساعدة للمزامنة
async function getStoredSales() {
  // جلب المبيعات المحفوظة محلياً
  return []
}

async function syncSalesToServer(sales) {
  // رفع المبيعات إلى الخادم
  console.log('Service Worker: Syncing sales to server', sales)
}
