import { z } from 'zod'
import { createTRPCRouter, publicProcedure } from '../init'
import { db } from '@/lib/db'
import { suppliers } from '@/lib/db/sqlite-schema'
import { eq } from 'drizzle-orm'

const supplierSchema = z.object({
  name: z.string().min(1, 'اسم المورد مطلوب'),
  company: z.string().optional(),
  phone: z.string().min(1, 'رقم الهاتف مطلوب'),
  email: z.string().email('البريد الإلكتروني غير صحيح').optional().or(z.literal('')),
  address: z.string().optional(),
  taxNumber: z.string().optional(),
  status: z.enum(['active', 'inactive']).default('active'),
})

export const suppliersRouter = createTRPCRouter({
  // جلب جميع الموردين
  getAll: publicProcedure.query(async () => {
    try {
      return await db.select().from(suppliers)
    } catch (error) {
      console.error('Error fetching suppliers:', error)
      return []
    }
  }),

  // جلب مورد واحد
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      try {
        const result = await db.select().from(suppliers).where(eq(suppliers.id, input.id))
        return result[0] || null
      } catch (error) {
        console.error('Error fetching supplier:', error)
        return null
      }
    }),

  // إضافة مورد جديد
  create: publicProcedure
    .input(supplierSchema)
    .mutation(async ({ input }) => {
      try {
        const newSupplier = {
          id: Date.now().toString(36) + Math.random().toString(36).substr(2),
          ...input,
          email: input.email || null,
          totalPurchases: 0,
          lastPurchase: null,
          companyId: 'default-company',
          createdAt: Date.now(),
          updatedAt: Date.now(),
        }
        
        await db.insert(suppliers).values(newSupplier)
        return newSupplier
      } catch (error) {
        console.error('Error creating supplier:', error)
        throw new Error('فشل في إضافة المورد')
      }
    }),

  // تحديث مورد
  update: publicProcedure
    .input(z.object({
      id: z.string(),
      data: supplierSchema.partial()
    }))
    .mutation(async ({ input }) => {
      try {
        const updatedData = {
          ...input.data,
          email: input.data.email || null,
          updatedAt: Date.now(),
        }
        
        await db.update(suppliers)
          .set(updatedData)
          .where(eq(suppliers.id, input.id))
        
        return { success: true }
      } catch (error) {
        console.error('Error updating supplier:', error)
        throw new Error('فشل في تحديث المورد')
      }
    }),

  // حذف مورد
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      try {
        await db.delete(suppliers).where(eq(suppliers.id, input.id))
        return { success: true }
      } catch (error) {
        console.error('Error deleting supplier:', error)
        throw new Error('فشل في حذف المورد')
      }
    }),

  // البحث في الموردين
  search: publicProcedure
    .input(z.object({
      query: z.string(),
      status: z.enum(['active', 'inactive']).optional(),
    }))
    .query(async ({ input }) => {
      try {
        let allSuppliers = await db.select().from(suppliers)
        
        // تطبيق فلتر الحالة
        if (input.status) {
          allSuppliers = allSuppliers.filter(supplier => supplier.status === input.status)
        }
        
        // البحث النصي
        if (input.query) {
          allSuppliers = allSuppliers.filter(supplier => 
            supplier.name.toLowerCase().includes(input.query.toLowerCase()) ||
            supplier.company?.toLowerCase().includes(input.query.toLowerCase()) ||
            supplier.phone.includes(input.query) ||
            supplier.email?.toLowerCase().includes(input.query.toLowerCase())
          )
        }
        
        return allSuppliers
      } catch (error) {
        console.error('Error searching suppliers:', error)
        return []
      }
    }),

  // إحصائيات الموردين
  getStats: publicProcedure.query(async () => {
    try {
      const allSuppliers = await db.select().from(suppliers)
      
      return {
        totalSuppliers: allSuppliers.length,
        activeSuppliers: allSuppliers.filter(s => s.status === 'active').length,
        inactiveSuppliers: allSuppliers.filter(s => s.status === 'inactive').length,
        totalPurchases: allSuppliers.reduce((sum, s) => sum + s.totalPurchases, 0),
        averagePurchases: allSuppliers.length > 0 ? 
          allSuppliers.reduce((sum, s) => sum + s.totalPurchases, 0) / allSuppliers.length : 0,
      }
    } catch (error) {
      console.error('Error fetching supplier stats:', error)
      return {
        totalSuppliers: 0,
        activeSuppliers: 0,
        inactiveSuppliers: 0,
        totalPurchases: 0,
        averagePurchases: 0,
      }
    }
  }),

  // تحديث إحصائيات المورد (للاستخدام الداخلي)
  updateStats: publicProcedure
    .input(z.object({
      id: z.string(),
      purchaseAmount: z.number(),
    }))
    .mutation(async ({ input }) => {
      try {
        const supplier = await db.select().from(suppliers).where(eq(suppliers.id, input.id))
        if (!supplier[0]) throw new Error('المورد غير موجود')
        
        await db.update(suppliers)
          .set({
            totalPurchases: supplier[0].totalPurchases + input.purchaseAmount,
            lastPurchase: Date.now(),
            updatedAt: Date.now(),
          })
          .where(eq(suppliers.id, input.id))
        
        return { success: true }
      } catch (error) {
        console.error('Error updating supplier stats:', error)
        throw new Error('فشل في تحديث إحصائيات المورد')
      }
    }),
})
