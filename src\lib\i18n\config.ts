// إعدادات الترجمة متعددة اللغات
import { createSharedPathnamesNavigation } from 'next-intl/navigation'

export const locales = ['ar', 'en'] as const
export type Locale = typeof locales[number]

export const defaultLocale: Locale = 'ar'

export const localeNames = {
  ar: 'العربية',
  en: 'English'
}

export const { Link, redirect, usePathname, useRouter } = createSharedPathnamesNavigation({ locales })

// إعدادات اتجاه النص
export const localeDirections = {
  ar: 'rtl',
  en: 'ltr'
} as const

// إعدادات العملة
export const localeCurrencies = {
  ar: 'SAR',
  en: 'USD'
} as const

// إعدادات التاريخ
export const localeDateFormats = {
  ar: 'ar-SA',
  en: 'en-US'
} as const
