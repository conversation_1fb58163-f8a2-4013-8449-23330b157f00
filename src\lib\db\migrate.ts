import { db } from './index'
import { readFileSync } from 'fs'
import { join } from 'path'

export async function runMigrations() {
  try {
    console.log('🔄 بدء تشغيل migrations...')
    
    // قراءة ملف migration
    const migrationPath = join(process.cwd(), 'drizzle', '0000_initial.sql')
    const migrationSQL = readFileSync(migrationPath, 'utf-8')
    
    // تقسيم SQL إلى statements منفصلة
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0)
    
    // تشغيل كل statement
    for (const statement of statements) {
      try {
        await db.run(statement)
      } catch (error: any) {
        // تجاهل أخطاء الجداول الموجودة بالفعل
        if (!error.message.includes('already exists')) {
          console.error('خطأ في تشغيل statement:', statement)
          throw error
        }
      }
    }
    
    console.log('✅ تم تشغيل migrations بنجاح')
    
  } catch (error) {
    console.error('❌ خطأ في تشغيل migrations:', error)
    throw error
  }
}

export async function initializeDatabase() {
  try {
    console.log('🚀 بدء تهيئة قاعدة البيانات...')
    
    await runMigrations()
    
    // استيراد وتشغيل seed
    const { seedDatabase } = await import('./seed')
    await seedDatabase()
    
    console.log('🎉 تم تهيئة قاعدة البيانات بنجاح!')
    
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error)
    throw error
  }
}
