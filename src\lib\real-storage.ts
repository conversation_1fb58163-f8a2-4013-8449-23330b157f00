// نظام إدارة البيانات الحقيقي مع Local Storage محسن
export interface Product {
  id: string
  name: string
  description: string
  category: string
  price: number
  cost: number
  stock: number
  minStock: number
  barcode?: string
  unit: string
  supplier: string
  location: string
  image?: string
  createdAt: string
  updatedAt: string
}

export interface Customer {
  id: string
  name: string
  company?: string
  phone: string
  email?: string
  address?: string
  type: 'individual' | 'company'
  status: 'active' | 'inactive'
  creditLimit: number
  currentDebt: number
  totalPurchases: number
  lastPurchase?: string
  createdAt: string
  updatedAt: string
}

export interface Sale {
  id: string
  customerId?: string
  customerName: string
  items: SaleItem[]
  subtotal: number
  discount: number
  tax: number
  total: number
  paymentType: 'cash' | 'credit' | 'partial'
  paidAmount: number
  remainingAmount: number
  status: 'completed' | 'pending' | 'cancelled'
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface SaleItem {
  id: string
  productId: string
  productName: string
  quantity: number
  unitPrice: number
  total: number
}

export interface Return {
  id: string
  saleId: string
  customerId?: string
  customerName: string
  items: ReturnItem[]
  total: number
  reason: string
  status: 'completed' | 'pending'
  notes?: string
  createdAt: string
}

export interface ReturnItem {
  id: string
  productId: string
  productName: string
  quantity: number
  unitPrice: number
  total: number
}

export interface Supplier {
  id: string
  name: string
  company?: string
  phone: string
  email?: string
  address?: string
  status: 'active' | 'inactive'
  totalPurchases: number
  lastPurchase?: string
  createdAt: string
  updatedAt: string
}

export interface StockMovement {
  id: string
  productId: string
  type: 'in' | 'out' | 'adjustment'
  quantity: number
  reason: string
  reference?: string
  createdAt: string
}

// نظام إدارة البيانات المحسن
class RealDataManager {
  private getStorageKey(type: string): string {
    return `steel_trade_v2_${type}`
  }

  // وظائف عامة للتخزين مع معالجة الأخطاء
  private getData<T>(type: string): T[] {
    if (typeof window === 'undefined') return []
    try {
      const data = localStorage.getItem(this.getStorageKey(type))
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error(`Error loading ${type}:`, error)
      return []
    }
  }

  private setData<T>(type: string, data: T[]): boolean {
    if (typeof window === 'undefined') return false
    try {
      localStorage.setItem(this.getStorageKey(type), JSON.stringify(data))
      return true
    } catch (error) {
      console.error(`Error saving ${type}:`, error)
      return false
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  private getCurrentDateTime(): string {
    return new Date().toISOString()
  }

  // إدارة المنتجات
  getProducts(): Product[] {
    return this.getData<Product>('products')
  }

  addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Product | null {
    try {
      const products = this.getProducts()
      const newProduct: Product = {
        ...product,
        id: this.generateId(),
        createdAt: this.getCurrentDateTime(),
        updatedAt: this.getCurrentDateTime()
      }
      products.push(newProduct)
      if (this.setData('products', products)) {
        return newProduct
      }
      return null
    } catch (error) {
      console.error('Error adding product:', error)
      return null
    }
  }

  updateProduct(id: string, updates: Partial<Product>): Product | null {
    try {
      const products = this.getProducts()
      const index = products.findIndex(p => p.id === id)
      if (index === -1) return null
      
      products[index] = { 
        ...products[index], 
        ...updates, 
        updatedAt: this.getCurrentDateTime() 
      }
      
      if (this.setData('products', products)) {
        return products[index]
      }
      return null
    } catch (error) {
      console.error('Error updating product:', error)
      return null
    }
  }

  deleteProduct(id: string): boolean {
    try {
      const products = this.getProducts()
      const filteredProducts = products.filter(p => p.id !== id)
      if (filteredProducts.length === products.length) return false
      
      return this.setData('products', filteredProducts)
    } catch (error) {
      console.error('Error deleting product:', error)
      return false
    }
  }

  // إدارة العملاء
  getCustomers(): Customer[] {
    return this.getData<Customer>('customers')
  }

  addCustomer(customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Customer | null {
    try {
      const customers = this.getCustomers()
      const newCustomer: Customer = {
        ...customer,
        id: this.generateId(),
        createdAt: this.getCurrentDateTime(),
        updatedAt: this.getCurrentDateTime()
      }
      customers.push(newCustomer)
      if (this.setData('customers', customers)) {
        return newCustomer
      }
      return null
    } catch (error) {
      console.error('Error adding customer:', error)
      return null
    }
  }

  updateCustomer(id: string, updates: Partial<Customer>): Customer | null {
    try {
      const customers = this.getCustomers()
      const index = customers.findIndex(c => c.id === id)
      if (index === -1) return null
      
      customers[index] = { 
        ...customers[index], 
        ...updates, 
        updatedAt: this.getCurrentDateTime() 
      }
      
      if (this.setData('customers', customers)) {
        return customers[index]
      }
      return null
    } catch (error) {
      console.error('Error updating customer:', error)
      return null
    }
  }

  deleteCustomer(id: string): boolean {
    try {
      const customers = this.getCustomers()
      const filteredCustomers = customers.filter(c => c.id !== id)
      if (filteredCustomers.length === customers.length) return false
      
      return this.setData('customers', filteredCustomers)
    } catch (error) {
      console.error('Error deleting customer:', error)
      return false
    }
  }

  // إدارة المبيعات
  getSales(): Sale[] {
    return this.getData<Sale>('sales')
  }

  addSale(saleData: Omit<Sale, 'id' | 'createdAt' | 'updatedAt'>): Sale | null {
    try {
      const sales = this.getSales()
      const newSale: Sale = {
        ...saleData,
        id: this.generateId(),
        createdAt: this.getCurrentDateTime(),
        updatedAt: this.getCurrentDateTime()
      }

      // تحديث المخزون
      for (const item of saleData.items) {
        if (!this.updateStock(item.productId, item.quantity, 'out', `بيع - ${newSale.id}`, newSale.id)) {
          console.error(`Failed to update stock for product ${item.productId}`)
          return null
        }
      }

      // تحديث بيانات العميل
      if (saleData.customerId) {
        const customer = this.getCustomers().find(c => c.id === saleData.customerId)
        if (customer) {
          customer.totalPurchases += saleData.total
          customer.lastPurchase = this.getCurrentDateTime()
          
          if (saleData.paymentType === 'credit' || saleData.paymentType === 'partial') {
            customer.currentDebt += saleData.remainingAmount
          }
          
          this.updateCustomer(customer.id, customer)
        }
      }

      sales.push(newSale)
      if (this.setData('sales', sales)) {
        return newSale
      }
      return null
    } catch (error) {
      console.error('Error adding sale:', error)
      return null
    }
  }

  // تحديث المخزون
  updateStock(productId: string, quantity: number, type: 'in' | 'out' | 'adjustment', reason: string, reference?: string): boolean {
    try {
      const products = this.getProducts()
      const product = products.find(p => p.id === productId)
      if (!product) return false

      const oldStock = product.stock
      if (type === 'in') {
        product.stock += quantity
      } else if (type === 'out') {
        if (product.stock < quantity) return false
        product.stock -= quantity
      } else {
        product.stock = quantity
      }

      product.updatedAt = this.getCurrentDateTime()
      
      if (this.setData('products', products)) {
        // تسجيل حركة المخزون
        this.addStockMovement({
          productId,
          type,
          quantity: type === 'adjustment' ? quantity - oldStock : quantity,
          reason,
          reference
        })
        return true
      }
      return false
    } catch (error) {
      console.error('Error updating stock:', error)
      return false
    }
  }

  // إدارة المرتجعات
  getReturns(): Return[] {
    return this.getData<Return>('returns')
  }

  addReturn(returnData: Omit<Return, 'id' | 'createdAt'>): Return | null {
    try {
      const returns = this.getReturns()
      const newReturn: Return = {
        ...returnData,
        id: this.generateId(),
        createdAt: this.getCurrentDateTime()
      }

      // تحديث المخزون - إرجاع الكميات
      for (const item of returnData.items) {
        if (!this.updateStock(item.productId, item.quantity, 'in', `مرتجع - ${newReturn.id}`, newReturn.id)) {
          console.error(`Failed to update stock for returned product ${item.productId}`)
          return null
        }
      }

      // تحديث بيانات العميل - تقليل الدين
      if (returnData.customerId) {
        const customers = this.getCustomers()
        const customer = customers.find(c => c.id === returnData.customerId)
        if (customer) {
          customer.currentDebt = Math.max(0, customer.currentDebt - returnData.total)
          customer.updatedAt = this.getCurrentDateTime()
          this.setData('customers', customers)
        }
      }

      returns.push(newReturn)
      if (this.setData('returns', returns)) {
        return newReturn
      }
      return null
    } catch (error) {
      console.error('Error adding return:', error)
      return null
    }
  }

  updateReturn(id: string, updates: Partial<Return>): Return | null {
    try {
      const returns = this.getReturns()
      const index = returns.findIndex(r => r.id === id)
      if (index === -1) return null

      returns[index] = {
        ...returns[index],
        ...updates
      }

      if (this.setData('returns', returns)) {
        return returns[index]
      }
      return null
    } catch (error) {
      console.error('Error updating return:', error)
      return null
    }
  }

  deleteReturn(id: string): boolean {
    try {
      const returns = this.getReturns()
      const filteredReturns = returns.filter(r => r.id !== id)
      if (filteredReturns.length === returns.length) return false

      return this.setData('returns', filteredReturns)
    } catch (error) {
      console.error('Error deleting return:', error)
      return false
    }
  }

  // إدارة الموردين
  getSuppliers(): Supplier[] {
    return this.getData<Supplier>('suppliers')
  }

  addSupplier(supplier: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt'>): Supplier | null {
    try {
      const suppliers = this.getSuppliers()
      const newSupplier: Supplier = {
        ...supplier,
        id: this.generateId(),
        createdAt: this.getCurrentDateTime(),
        updatedAt: this.getCurrentDateTime()
      }
      suppliers.push(newSupplier)
      if (this.setData('suppliers', suppliers)) {
        return newSupplier
      }
      return null
    } catch (error) {
      console.error('Error adding supplier:', error)
      return null
    }
  }

  updateSupplier(id: string, updates: Partial<Supplier>): Supplier | null {
    try {
      const suppliers = this.getSuppliers()
      const index = suppliers.findIndex(s => s.id === id)
      if (index === -1) return null

      suppliers[index] = {
        ...suppliers[index],
        ...updates,
        updatedAt: this.getCurrentDateTime()
      }

      if (this.setData('suppliers', suppliers)) {
        return suppliers[index]
      }
      return null
    } catch (error) {
      console.error('Error updating supplier:', error)
      return null
    }
  }

  deleteSupplier(id: string): boolean {
    try {
      const suppliers = this.getSuppliers()
      const filteredSuppliers = suppliers.filter(s => s.id !== id)
      if (filteredSuppliers.length === suppliers.length) return false

      return this.setData('suppliers', filteredSuppliers)
    } catch (error) {
      console.error('Error deleting supplier:', error)
      return false
    }
  }

  // إدارة حركات المخزون
  getStockMovements(): StockMovement[] {
    return this.getData<StockMovement>('stock_movements')
  }

  addStockMovement(movement: Omit<StockMovement, 'id' | 'createdAt'>): StockMovement | null {
    try {
      const movements = this.getStockMovements()
      const newMovement: StockMovement = {
        ...movement,
        id: this.generateId(),
        createdAt: this.getCurrentDateTime()
      }
      movements.push(newMovement)
      if (this.setData('stock_movements', movements)) {
        return newMovement
      }
      return null
    } catch (error) {
      console.error('Error adding stock movement:', error)
      return null
    }
  }

  // تهيئة البيانات الأولية
  initializeData(): void {
    if (this.getProducts().length === 0) {
      this.initializeProducts()
    }
    if (this.getCustomers().length === 0) {
      this.initializeCustomers()
    }
  }

  private initializeProducts(): void {
    const initialProducts = [
      {
        name: 'حديد تسليح 8 مم',
        description: 'حديد تسليح عالي الجودة مقاس 8 مم',
        category: 'حديد التسليح',
        price: 850,
        cost: 750,
        stock: 500,
        minStock: 100,
        barcode: '8901234567890',
        unit: 'طن',
        supplier: 'شركة الحديد المتحدة',
        location: 'المستودع الرئيسي - رف A1'
      },
      {
        name: 'حديد تسليح 10 مم',
        description: 'حديد تسليح عالي الجودة مقاس 10 مم',
        category: 'حديد التسليح',
        price: 870,
        cost: 770,
        stock: 400,
        minStock: 80,
        barcode: '8901234567891',
        unit: 'طن',
        supplier: 'شركة الحديد المتحدة',
        location: 'المستودع الرئيسي - رف A2'
      }
    ]

    initialProducts.forEach(product => this.addProduct(product))
  }

  private initializeCustomers(): void {
    const initialCustomers = [
      {
        name: 'أحمد محمد',
        phone: '*********',
        type: 'individual' as const,
        status: 'active' as const,
        creditLimit: 50000,
        currentDebt: 0,
        totalPurchases: 0
      },
      {
        name: 'شركة البناء الحديث',
        company: 'شركة البناء الحديث المحدودة',
        phone: '*********',
        email: '<EMAIL>',
        type: 'company' as const,
        status: 'active' as const,
        creditLimit: 200000,
        currentDebt: 0,
        totalPurchases: 0
      }
    ]

    initialCustomers.forEach(customer => this.addCustomer(customer))
  }

  // مسح جميع البيانات (للاختبار)
  clearAllData(): boolean {
    try {
      const types = ['products', 'customers', 'sales', 'returns', 'suppliers', 'stock_movements']
      for (const type of types) {
        localStorage.removeItem(this.getStorageKey(type))
      }
      return true
    } catch (error) {
      console.error('Error clearing data:', error)
      return false
    }
  }
}

// إنشاء مثيل واحد للاستخدام في جميع أنحاء التطبيق
export const realDataManager = new RealDataManager()

// تهيئة البيانات عند تحميل الوحدة
if (typeof window !== 'undefined') {
  realDataManager.initializeData()
}
