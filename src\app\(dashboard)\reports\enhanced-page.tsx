'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  ArrowLeft, TrendingUp, DollarSign, Package, Users, BarChart3, 
  Download, Calendar, FileText, PieChart, Activity, Target, AlertTriangle
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { realDataManager, Sale, Product, Customer } from '@/lib/real-storage'

export default function EnhancedReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'year'>('month')
  const [selectedReport, setSelectedReport] = useState<'overview' | 'financial' | 'products' | 'customers'>('overview')
  const [sales, setSales] = useState<Sale[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])

  useEffect(() => {
    realDataManager.initializeData()
    loadData()
  }, [])

  const loadData = () => {
    setSales(realDataManager.getSales())
    setProducts(realDataManager.getProducts())
    setCustomers(realDataManager.getCustomers())
  }

  // تصفية البيانات حسب الفترة
  const getFilteredSales = () => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    return sales.filter(sale => {
      const saleDate = new Date(sale.createdAt)
      switch (selectedPeriod) {
        case 'today':
          return saleDate >= today
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          return saleDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
          return saleDate >= monthAgo
        case 'year':
          const yearAgo = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000)
          return saleDate >= yearAgo
        default:
          return true
      }
    })
  }

  const filteredSales = getFilteredSales()

  // حساب الإحصائيات الشاملة
  const calculateStats = () => {
    const totalRevenue = filteredSales.reduce((sum, sale) => sum + sale.total, 0)
    const totalOrders = filteredSales.length
    const totalCustomers = new Set(filteredSales.map(sale => sale.customerId).filter(Boolean)).size
    
    const totalCost = filteredSales.reduce((sum, sale) => {
      return sum + sale.items.reduce((itemSum, item) => {
        const product = products.find(p => p.id === item.productId)
        return itemSum + (product ? product.cost * item.quantity : 0)
      }, 0)
    }, 0)

    const totalProfit = totalRevenue - totalCost
    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

    return {
      totalRevenue,
      totalOrders,
      totalCustomers,
      totalCost,
      totalProfit,
      profitMargin,
      averageOrderValue,
      inventoryValue: products.reduce((sum, p) => sum + (p.stock * p.cost), 0),
      lowStockItems: products.filter(p => p.stock <= p.minStock).length,
      outOfStockItems: products.filter(p => p.stock === 0).length
    }
  }

  const stats = calculateStats()

  // أفضل المنتجات
  const getTopProducts = () => {
    const productSales = new Map()
    
    filteredSales.forEach(sale => {
      sale.items.forEach(item => {
        const current = productSales.get(item.productId) || { 
          name: item.productName, 
          quantity: 0, 
          revenue: 0 
        }
        current.quantity += item.quantity
        current.revenue += item.total
        productSales.set(item.productId, current)
      })
    })
    
    return Array.from(productSales.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5)
  }

  // أفضل العملاء
  const getTopCustomers = () => {
    const customerSales = new Map()
    
    filteredSales.forEach(sale => {
      const current = customerSales.get(sale.customerId || 'unknown') || {
        name: sale.customerName,
        orders: 0,
        revenue: 0
      }
      current.orders += 1
      current.revenue += sale.total
      customerSales.set(sale.customerId || 'unknown', current)
    })
    
    return Array.from(customerSales.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5)
  }

  // مبيعات حسب الفئة
  const getSalesByCategory = () => {
    const categorySales = new Map()
    
    filteredSales.forEach(sale => {
      sale.items.forEach(item => {
        const product = products.find(p => p.id === item.productId)
        if (product) {
          const current = categorySales.get(product.category) || 0
          categorySales.set(product.category, current + item.total)
        }
      })
    })
    
    return Array.from(categorySales.entries())
      .map(([category, revenue]) => ({ category, revenue }))
      .sort((a, b) => b.revenue - a.revenue)
  }

  const topProducts = getTopProducts()
  const topCustomers = getTopCustomers()
  const salesByCategory = getSalesByCategory()

  // مكون مخطط بياني بسيط
  const SimpleBarChart = ({ data, title }: { data: any[], title: string }) => {
    const maxValue = Math.max(...data.map(item => item.revenue || item.value || 0))
    
    return (
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">{title}</h4>
        <div className="space-y-3">
          {data.slice(0, 5).map((item, index) => {
            const value = item.revenue || item.value || 0
            const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0
            
            return (
              <div key={index} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">{item.name || item.category}</span>
                  <span className="font-medium">{formatCurrency(value)}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  const periods = [
    { value: 'today', label: 'اليوم' },
    { value: 'week', label: 'هذا الأسبوع' },
    { value: 'month', label: 'هذا الشهر' },
    { value: 'year', label: 'هذا العام' }
  ]

  const reports = [
    { value: 'overview', label: 'نظرة عامة', icon: BarChart3 },
    { value: 'financial', label: 'التقرير المالي', icon: DollarSign },
    { value: 'products', label: 'تقرير المنتجات', icon: Package },
    { value: 'customers', label: 'تقرير العملاء', icon: Users }
  ]

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
                <ArrowLeft className="h-4 w-4 ml-2" />
                العودة
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">التقارير المتقدمة</h1>
                <p className="text-sm text-gray-600">تحليل شامل لأداء الأعمال</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <Select value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {periods.map(period => (
                    <SelectItem key={period.value} value={period.value}>
                      {period.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                تصدير التقرير
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* نوع التقرير */}
        <div className="flex gap-2 mb-8">
          {reports.map(report => {
            const Icon = report.icon
            return (
              <Button
                key={report.value}
                variant={selectedReport === report.value ? 'default' : 'outline'}
                onClick={() => setSelectedReport(report.value as any)}
                className="flex items-center gap-2"
              >
                <Icon className="h-4 w-4" />
                {report.label}
              </Button>
            )
          })}
        </div>

        {/* المحتوى */}
        {selectedReport === 'overview' && (
          <div className="space-y-8">
            {/* الإحصائيات الرئيسية */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">إجمالي المبيعات</p>
                      <p className="text-2xl font-bold text-green-600">
                        {formatCurrency(stats.totalRevenue)}
                      </p>
                    </div>
                    <div className="bg-green-100 p-3 rounded-full">
                      <DollarSign className="h-6 w-6 text-green-600" />
                    </div>
                  </div>
                  <div className="flex items-center mt-2 text-sm">
                    <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
                    <span className="text-green-500">+12.5%</span>
                    <span className="text-gray-500 mr-2">من الفترة السابقة</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">صافي الربح</p>
                      <p className="text-2xl font-bold text-blue-600">
                        {formatCurrency(stats.totalProfit)}
                      </p>
                    </div>
                    <div className="bg-blue-100 p-3 rounded-full">
                      <Target className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                  <div className="flex items-center mt-2 text-sm">
                    <span className="text-blue-500">{stats.profitMargin.toFixed(1)}%</span>
                    <span className="text-gray-500 mr-2">هامش الربح</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">عدد الطلبات</p>
                      <p className="text-2xl font-bold text-purple-600">
                        {stats.totalOrders}
                      </p>
                    </div>
                    <div className="bg-purple-100 p-3 rounded-full">
                      <Package className="h-6 w-6 text-purple-600" />
                    </div>
                  </div>
                  <div className="flex items-center mt-2 text-sm">
                    <span className="text-gray-600">متوسط الطلب:</span>
                    <span className="text-purple-600 mr-1">{formatCurrency(stats.averageOrderValue)}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">العملاء النشطون</p>
                      <p className="text-2xl font-bold text-orange-600">
                        {stats.totalCustomers}
                      </p>
                    </div>
                    <div className="bg-orange-100 p-3 rounded-full">
                      <Users className="h-6 w-6 text-orange-600" />
                    </div>
                  </div>
                  <div className="flex items-center mt-2 text-sm">
                    <Activity className="h-4 w-4 text-orange-500 ml-1" />
                    <span className="text-orange-500">نشط</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* المخططات */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>أفضل المنتجات مبيعاً</CardTitle>
                </CardHeader>
                <CardContent>
                  <SimpleBarChart data={topProducts} title="المبيعات حسب المنتج" />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>المبيعات حسب الفئة</CardTitle>
                </CardHeader>
                <CardContent>
                  <SimpleBarChart data={salesByCategory} title="المبيعات حسب الفئة" />
                </CardContent>
              </Card>
            </div>

            {/* أفضل العملاء */}
            <Card>
              <CardHeader>
                <CardTitle>أفضل العملاء</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topCustomers.map((customer, index) => (
                    <div key={index} className="flex items-center justify-between border-b pb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-medium">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium">{customer.name}</p>
                          <p className="text-sm text-gray-600">عدد الطلبات: {customer.orders}</p>
                        </div>
                      </div>
                      <div className="text-left">
                        <p className="font-bold text-green-600">{formatCurrency(customer.revenue)}</p>
                        <p className="text-sm text-gray-600">
                          متوسط الطلب: {formatCurrency(customer.revenue / customer.orders)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
