import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Toaster } from '@/components/ui/toaster'
// import PWAInstaller, { ConnectionStatus } from '@/components/pwa/PWAInstaller'
// import IconGenerator from '@/components/pwa/IconGenerator'
import { AuthProvider } from '@/lib/simple-auth'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'نظام المحاسبة الحديث',
  description: 'نظام محاسبة متطور وحديث لإدارة الأعمال',
  keywords: ['محاسبة', 'فواتير', 'إدارة مالية', 'تقارير'],
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'نظام المحاسبة'
  },
  icons: {
    icon: '/favicon.ico'
  }
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#2563eb'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthProvider>
      <html lang="ar" dir="rtl">
        <body className={`${inter.className} rtl`}>
          {children}
          <Toaster />
        </body>
      </html>
    </AuthProvider>
  )
}
