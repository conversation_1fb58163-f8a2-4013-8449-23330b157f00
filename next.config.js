/** @type {import('next').NextConfig} */
const nextConfig = {
  serverExternalPackages: ['postgres', 'better-sqlite3'],
  images: {
    domains: ['images.clerk.dev'],
  },
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },

  webpack: (config, { isServer, dev }) => {
    if (isServer) {
      config.externals.push('better-sqlite3')
    }

    // إصلاح مشكلة webpack في التطوير
    if (dev && !isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
        buffer: false,
        util: false,
      }

      // تجاهل تحذيرات المكتبات الاختيارية
      config.ignoreWarnings = [
        { module: /node_modules\/quagga/ },
        { module: /node_modules\/jspdf/ },
        { module: /node_modules\/html2canvas/ },
        { module: /node_modules\/xlsx/ },
        { module: /node_modules\/chart\.js/ },
        { module: /node_modules\/react-chartjs-2/ },
        /Critical dependency: the request of a dependency is an expression/,
      ]

      // تحسين splitChunks
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 244000,
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
            },
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              priority: 5,
              reuseExistingChunk: true,
            },
          },
        },
      }
    }

    return config
  },
}

module.exports = nextConfig
