import { formatCurrency } from '@/lib/utils'

describe('formatCurrency', () => {
  test('should format positive numbers correctly', () => {
    expect(formatCurrency(1000)).toBe('1,000 ر.ي')
    expect(formatCurrency(1234.56)).toBe('1,235 ر.ي')
    expect(formatCurrency(0)).toBe('0 ر.ي')
  })

  test('should format negative numbers correctly', () => {
    expect(formatCurrency(-1000)).toBe('-1,000 ر.ي')
    expect(formatCurrency(-1234.56)).toBe('-1,235 ر.ي')
  })

  test('should handle decimal numbers', () => {
    expect(formatCurrency(999.99)).toBe('1,000 ر.ي')
    expect(formatCurrency(1000.01)).toBe('1,000 ر.ي')
  })

  test('should handle large numbers', () => {
    expect(formatCurrency(1000000)).toBe('1,000,000 ر.ي')
    expect(formatCurrency(1234567.89)).toBe('1,234,568 ر.ي')
  })
})
