const Database = require('better-sqlite3')
const { readFileSync, mkdirSync } = require('fs')
const { join } = require('path')

async function runMigrations() {
  try {
    console.log('🔄 بدء تشغيل migrations...')

    // إنشاء مجلد البيانات
    const dataDir = join(process.cwd(), 'data')
    try {
      mkdirSync(dataDir, { recursive: true })
    } catch (error) {
      // المجلد موجود بالفعل
    }

    // إنشاء قاعدة البيانات
    const dbPath = join(dataDir, 'steel-trade.db')
    const db = new Database(dbPath)

    // قراءة ملف migration
    const migrationPath = join(process.cwd(), 'drizzle', '0000_initial.sql')
    const migrationSQL = readFileSync(migrationPath, 'utf-8')

    // تقسيم SQL إلى statements منفصلة
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0)

    // تشغيل كل statement
    for (const statement of statements) {
      try {
        db.exec(statement)
      } catch (error) {
        // تجاهل أخطاء الجداول الموجودة بالفعل
        if (!error.message.includes('already exists')) {
          console.error('خطأ في تشغيل statement:', statement)
          throw error
        }
      }
    }

    console.log('✅ تم تشغيل migrations بنجاح')

    // إضافة البيانات الأولية
    await seedDatabase(db)

    db.close()

  } catch (error) {
    console.error('❌ خطأ في تشغيل migrations:', error)
    throw error
  }
}

async function seedDatabase(db) {
  try {
    console.log('🌱 بدء إضافة البيانات الأولية...')

    // إضافة شركة افتراضية
    const insertCompany = db.prepare(`
      INSERT OR IGNORE INTO companies (id, name, email, phone, address, tax_number, logo, user_id, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    insertCompany.run(
      'default-company',
      'تجارة الحديد والمعدات',
      '<EMAIL>',
      '+967-1-234567',
      'صنعاء، اليمن',
      '*********',
      null,
      'default-user',
      Date.now(),
      Date.now()
    )

    console.log('✅ تم إضافة الشركة الافتراضية')

    // إضافة عملاء تجريبيين
    const insertCustomer = db.prepare(`
      INSERT OR IGNORE INTO customers (id, name, company, email, phone, address, tax_number, type, status, credit_limit, current_debt, total_purchases, last_purchase, company_id, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const customers = [
      ['customer-1', 'أحمد محمد علي', 'شركة البناء الحديث', '<EMAIL>', '+967-**********', 'شارع الزبيري، صنعاء', '*********', 'company', 'active', 500000, 0, 0, null, 'default-company', 1, Date.now(), Date.now()],
      ['customer-2', 'فاطمة سالم', null, '<EMAIL>', '+967-**********', 'حي السبعين، صنعاء', null, 'individual', 'active', 100000, 0, 0, null, 'default-company', 1, Date.now(), Date.now()],
      ['customer-3', 'محمد عبدالله', 'مؤسسة الإعمار للمقاولات', '<EMAIL>', '+967-**********', 'شارع الستين، صنعاء', '*********', 'company', 'active', 1000000, 0, 0, null, 'default-company', 1, Date.now(), Date.now()]
    ]

    customers.forEach(customer => insertCustomer.run(...customer))
    console.log('✅ تم إضافة العملاء التجريبيين')

    // إضافة منتجات تجريبية
    const insertProduct = db.prepare(`
      INSERT OR IGNORE INTO products (id, name, description, price, cost, unit, sku, barcode, category, stock, min_stock, supplier, location, image, company_id, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const products = [
      ['product-1', 'حديد تسليح 12 مم', 'حديد تسليح عالي الجودة قطر 12 مم', 850000, 750000, 'طن', 'STEEL-12MM', '*********0123', 'حديد التسليح', 50, 10, 'مصنع الحديد الوطني', 'المخزن الرئيسي - رف A1', null, 'default-company', 1, Date.now(), Date.now()],
      ['product-2', 'حديد تسليح 16 مم', 'حديد تسليح عالي الجودة قطر 16 مم', 860000, 760000, 'طن', 'STEEL-16MM', '*********0124', 'حديد التسليح', 30, 5, 'مصنع الحديد الوطني', 'المخزن الرئيسي - رف A2', null, 'default-company', 1, Date.now(), Date.now()],
      ['product-3', 'صاج مجلفن 2 مم', 'صاج مجلفن سماكة 2 مم مقاس 1.2 × 2.4 متر', 45000, 38000, 'لوح', 'SHEET-2MM', '*********0125', 'الصاج والألواح', 100, 20, 'مصنع الصاج المتطور', 'المخزن الثانوي - رف B1', null, 'default-company', 1, Date.now(), Date.now()],
      ['product-4', 'أنبوب حديد 2 بوصة', 'أنبوب حديد أسود قطر 2 بوصة', 25000, 20000, 'متر', 'PIPE-2INCH', '*********0126', 'الأنابيب', 200, 50, 'مصنع الأنابيب الحديثة', 'المخزن الخارجي - منطقة C', null, 'default-company', 1, Date.now(), Date.now()],
      ['product-5', 'مسامير 10 سم', 'مسامير حديد طول 10 سم', 15000, 12000, 'كيلو', 'NAILS-10CM', '*********0127', 'المسامير والبراغي', 500, 100, 'مصنع المسامير الذهبية', 'المخزن الرئيسي - رف D1', null, 'default-company', 1, Date.now(), Date.now()]
    ]

    products.forEach(product => insertProduct.run(...product))
    console.log('✅ تم إضافة المنتجات التجريبية')

    // إضافة موردين تجريبيين
    const insertSupplier = db.prepare(`
      INSERT OR IGNORE INTO suppliers (id, name, company, email, phone, address, tax_number, status, total_purchases, last_purchase, company_id, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const suppliers = [
      ['supplier-1', 'مصنع الحديد الوطني', 'شركة الحديد الوطنية المحدودة', '<EMAIL>', '+967-1-345678', 'المنطقة الصناعية، صنعاء', '*********', 'active', 0, null, 'default-company', Date.now(), Date.now()],
      ['supplier-2', 'مصنع الصاج المتطور', 'مؤسسة الصاج المتطور', '<EMAIL>', '+967-1-456789', 'شارع الصناعة، صنعاء', '*********', 'active', 0, null, 'default-company', Date.now(), Date.now()],
      ['supplier-3', 'مصنع الأنابيب الحديثة', 'شركة الأنابيب الحديثة', '<EMAIL>', '+967-1-567890', 'المنطقة الحرة، عدن', '*********', 'active', 0, null, 'default-company', Date.now(), Date.now()]
    ]

    suppliers.forEach(supplier => insertSupplier.run(...supplier))
    console.log('✅ تم إضافة الموردين التجريبيين')

    console.log('🎉 تم إكمال إضافة البيانات الأولية بنجاح!')

  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات الأولية:', error)
    throw error
  }
}

async function main() {
  try {
    console.log('🚀 بدء تهيئة قاعدة البيانات...')
    await runMigrations()
    console.log('🎉 تم تهيئة قاعدة البيانات بنجاح!')
    process.exit(0)
  } catch (error) {
    console.error('Migration failed:', error)
    process.exit(1)
  }
}

main()
