import { drizzle } from 'drizzle-orm/better-sqlite3'
import Database from 'better-sqlite3'
import * as schema from './sqlite-schema'
import { join } from 'path'

// إنشاء قاعدة البيانات في مجلد البيانات
const dbPath = join(process.cwd(), 'data', 'steel-trade.db')

// إنشاء مجلد البيانات إذا لم يكن موجوداً
import { mkdirSync } from 'fs'
try {
  mkdirSync(join(process.cwd(), 'data'), { recursive: true })
} catch (error) {
  // المجلد موجود بالفعل
}

const sqlite = new Database(dbPath)

// إضافة وظيفة run للـ sqlite instance
const dbWithRun = {
  ...drizzle(sqlite, { schema }),
  run: (sql: string) => sqlite.exec(sql)
}

export const db = dbWithRun

export type Database = typeof db;
